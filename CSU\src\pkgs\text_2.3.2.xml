<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/text" name="text" jsClass="AppMagic.Controls.Text" version="2.3.2" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[TEXT
      Control description here.]]></description>
  <requires>
    <require type="css" src="css/text.css" />
    <require type="javascript" src="js/text.js" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoBorders="true" autoFocusedBorders="true" autoFill="true" autoPointerViewState="true" autoDisabledViewState="true" autoBorderRadius="true" isVersionFlexible="true" supportsSetFocus="true" />
  <appMagic:accessibilityChecks controlIsInteractive="true" />
  <content><![CDATA[
  <div class="appmagic-textbox" touch-action="pan-x pan-y"
    appmagic-control="__WID__">
  <!-- ko if: (mode() === "multiline") -->
  <textarea
    appmagic-control="__WID__textarea"
    class="appmagic-textarea mousetrap block-undo-redo"
    data-control-part="text"
    data-bind="
      value: text,
      css: {
        underline: properties.Underline,
        strikethrough: properties.Strikethrough,
        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View
      },
      event: {
        click: handleClick,
        change: handleOnChange
      },
      attr: {
        title: properties.Tooltip() || null,
        'aria-label': properties.AccessibleLabel() || null,
        placeholder: properties.HintText,
        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,
        maxlength: properties.MaxLength() < 0 ? 0 : properties.MaxLength()
      },
      style: {
        fontFamily: properties.Font,
        fontSize: properties.Size,
        color: autoProperties.Color,
        fontWeight: properties.FontWeight,
        fontStyle: properties.Italic,
        textAlign: properties.Align,
        lineHeight: properties.LineHeight,
        paddingTop: properties.PaddingTop,
        paddingRight: properties.PaddingRight,
        paddingBottom: properties.PaddingBottom,
        paddingLeft: properties.PaddingLeft
      },
      disable: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled">
  </textarea>
  <!-- /ko -->
  <!-- ko if: (mode() !== "multiline") -->
  <input
    appmagic-control="__WID__textbox"
    class="appmagic-text mousetrap block-undo-redo"
    maxlength="10000"
    data-bind="
      attr: {
        type: mode() === 'password' ? 'password' : 'text',
        title: properties.Tooltip() || null,
        'aria-label': properties.AccessibleLabel() || null,
        placeholder: properties.HintText,
        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,
        'data-control-part': properties.Clear() ? 'text clearable' : 'text',
        inputmode: keyboardMode,
        maxlength: properties.MaxLength() < 0 ? 0 : properties.MaxLength()
      },
      css: {
        underline: properties.Underline,
        strikethrough: properties.Strikethrough,
        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View
      },
      value: text,
      event: {
        click: handleClick,
        change: handleOnChange
      },
      style: {
        fontFamily: properties.Font,
        fontSize: properties.Size,
        color: autoProperties.Color,
        fontWeight: properties.FontWeight,
        fontStyle: properties.Italic,
        textAlign: properties.Align,
        lineHeight: properties.LineHeight,
        paddingTop: properties.PaddingTop,
        paddingRight: properties.PaddingRight,
        paddingBottom: properties.PaddingBottom,
        paddingLeft: properties.PaddingLeft
      },
      disable: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled">
  </input>
  <div class='appmagic-text-clear-container'>
    <button class='appmagic-text-clear-button'
      data-control-part="clear"
      data-bind="
        visible: isFocused() && properties.Clear() && properties.Text() && mode() !== 'password' && viewState.displayMode() === AppMagic.Constants.DisplayMode.Edit,
        event: {click: handleClearClick},
        attr: {'aria-label': AppMagic.Strings.TextInputClearButtonLabel},
        style: {color: autoProperties.Color}">
        <svg
          class='appmagic-text-clear-svg'
          xmlns='http://www.w3.org/2000/svg'
          viewbox='0 0 12 12'
          aria-hidden='true'
          focusable='false'>
          <polygon points="12,1.1 10.9,0 6,4.9 1.1,0 0,1.1 4.9,6 0,10.9 1.1,12 6,7.1 10.9,12 12,10.9 7.1,6"></polygon>
        </svg>
    </button>
  </div>
  <!-- /ko -->
  <!-- ko if: properties.Format() === 'number' && viewState.displayMode() === AppMagic.Constants.DisplayMode.Edit -->
  <div class="a11y" aria-live="assertive" aria-atomic="true"></div>
  <!-- /ko -->
  </div>
    ]]></content>
  <properties>
    <property name="Default" localizedName="##text_Default##" datatype="String" defaultValue="##Text_DefaultValue_Default##" isExpr="true" editable="true" direction="in" isPrimaryInputProperty="true">
      <title>Initial text</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##text_Default_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_Default_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Text" localizedName="##text_Text##" datatype="String" direction="out" isPrimaryOutputProperty="true" supportsAutomation="true">
      <title>Output Text</title>
      <appMagic:category>data</appMagic:category>
    </property>
    <property name="Mode" localizedName="##text_Mode##" datatype="TextMode" defaultValue="%TextMode.RESERVED%.SingleLine" isExpr="true">
      <title>Textbox Mode</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>TextMode</appMagic:helperUI>
      <appMagic:displayName>##text_Mode_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_Mode_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Format" localizedName="##text_Format##" datatype="TextFormat" defaultValue="%TextFormat.RESERVED%.Text" isExpr="true">
      <title>Input textbox Format</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>TextFormat</appMagic:helperUI>
      <appMagic:displayName>##text_Format_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_Format_Tooltip##</appMagic:tooltip>
    </property>
    <property name="VirtualKeyboardMode" localizedName="##text_VirtualKeyboardMode##" datatype="VirtualKeyboardMode" defaultValue="%VirtualKeyboardMode.RESERVED%.Auto" isExpr="true">
      <title>Input Keyboard Type</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>VirtualKeyboardMode</appMagic:helperUI>
      <appMagic:displayName>##text_VirtualKeyboardMode_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_VirtualKeyboardMode_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Clear" localizedName="##text_Clear##" datatype="Boolean" defaultValue="false">
      <title>Clear Button</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>boolean</appMagic:helperUI>
      <appMagic:displayName>##text_Clear_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_Clear_Tooltip##</appMagic:tooltip>
    </property>
    <property name="EnableSpellCheck" localizedName="##text_EnableSpellCheck##" datatype="Boolean" defaultValue="false">
      <title>Enable spell check</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>boolean</appMagic:helperUI>
      <appMagic:displayName>##text_EnableSpellCheck_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_EnableSpellCheck_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Reset" localizedName="##commonProperties_Reset##" datatype="Boolean" defaultValue="false" direction="in">
      <title>Reset</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip>
    </property>
    <property name="LineHeight" localizedName="##text_LineHeight##" datatype="Number" defaultValue="1.2">
      <title>Line Height</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##text_LineHeight_DisplayName##</appMagic:displayName>
      <appMagic:helperUI>lineWidth</appMagic:helperUI>
      <appMagic:tooltip>##label_LineHeight_Tooltip##</appMagic:tooltip>
    </property>
    <property name="HintText" localizedName="##text_HintText##" datatype="String">
      <title>Hint text</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##text_HintText_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_HintText_Tooltip##</appMagic:tooltip>
    </property>
    <property name="MaxLength" localizedName="##commonProperties_MaxLength##" datatype="Number" default="" direction="in" isExpr="true">
      <title>MaxLength</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_MaxLength_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_MaxLength_Tooltip##</appMagic:tooltip>
    </property>
    <property name="DelayOutput" localizedName="##text_DelayOutput##" datatype="Boolean" defaultValue="false" direction="in" isExpr="true">
      <title>DelayOutput</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##text_DelayOutput_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##text_DelayOutput_Tooltip##</appMagic:tooltip>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Data -->
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="Tooltip">
      <appMagic:commandBar>
        <appMagic:visible>true</appMagic:visible>
      </appMagic:commandBar>
    </appMagic:includeProperty>
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="BorderColor" />
    <appMagic:includeProperty name="RadiusTopLeft" defaultValue="5" />
    <appMagic:includeProperty name="RadiusTopRight" defaultValue="5" />
    <appMagic:includeProperty name="RadiusBottomLeft" defaultValue="5" />
    <appMagic:includeProperty name="RadiusBottomRight" defaultValue="5" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="ColorFade(Self.BorderColor, 40%)" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="Self.HoverBorderColor" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" defaultValue="1" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="3" />
    <appMagic:includeProperty name="Color" defaultValue="RGBA(70, 68, 64, 1)" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="ColorFade(Self.Color, 40%)" />
    <appMagic:includeProperty name="PressedColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="HoverColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="Fill" defaultValue="RGBA(255, 255, 255, 1)" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="PressedFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="HoverFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="Font" />
    <appMagic:includeProperty name="Size" defaultValue="14" phoneDefaultValue="24" webDefaultValue="12" />
    <appMagic:includeProperty name="FontWeight" />
    <appMagic:includeProperty name="Italic" />
    <appMagic:includeProperty name="Underline" />
    <appMagic:includeProperty name="Strikethrough" />
    <appMagic:includeProperty name="Align" />
    <appMagic:includeProperty name="PaddingTop" defaultValue="5" />
    <appMagic:includeProperty name="PaddingRight" defaultValue="5" />
    <appMagic:includeProperty name="PaddingBottom" defaultValue="5" />
    <appMagic:includeProperty name="PaddingLeft" defaultValue="12" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="320" phoneDefaultValue="560" webDefaultValue="180" />
    <appMagic:includeProperty name="Height" defaultValue="40" phoneDefaultValue="70" webDefaultValue="32" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="DisplayMode" />
    <appMagic:includeProperty name="TabIndex" />
    <!-- Behavior -->
    <!-- TASK: 85476: Do Behavior properties make sense as input only? -->
    <appMagic:includeProperty name="OnChange" direction="in" />
    <appMagic:includeProperty name="OnSelect" direction="in" />
    <!--Hidden properties -->
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="30" />
    <appMagic:includeProperty name="minimumWidth" defaultValue="10" />
  </appMagic:includeProperties>
  <!--Property Dependencies -->
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="Default" output="Text" />
    <appMagic:propertyDependency input="MaxLength" output="Text" />
    <appMagic:propertyDependency input="Format" output="Text" />
    <appMagic:propertyDependency input="Reset" output="Text" />
  </appMagic:propertyDependencies>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Popular" priority="30" />
    <appMagic:category name="Input" priority="20" />
    <appMagic:category name="ClassicControls" priority="20" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Default" />
      <appMagic:property name="Format" />
      <appMagic:property name="HintText" />
      <appMagic:property name="Font" displayType="FontEnum" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="Size" labelOverride="##FontSize_Property##" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="FontWeight" displayType="EnumIcon" itemsOrder="Bold;Semibold;Normal;Lighter" showInCommandBar="true" />
      <appMagic:propertyGroup name="Style">
        <appMagic:property name="Italic" displayType="ToggleButton" />
        <appMagic:property name="Underline" displayType="ToggleButton" />
        <appMagic:property name="Strikethrough" displayType="ToggleButton" />
      </appMagic:propertyGroup>
      <appMagic:property name="Align" displayType="EnumButtons" itemsOrder="Left;Center;Right;Justify" labelOverride="##FontAlign_Property##" showInFloatie="true" showInCommandBar="true" floatieDisplayType="FaceplateIconEnum" />
      <appMagic:property name="LineHeight" />
      <appMagic:property name="Clear" />
      <appMagic:property name="EnableSpellCheck" />
      <appMagic:property name="MaxLength" />
      <appMagic:property name="Mode" />
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Padding">
        <appMagic:property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <appMagic:property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <appMagic:property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <appMagic:property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Color" showInFloatie="true" showInCommandBar="true" />
        <appMagic:property name="Fill" showInFloatie="true" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" showInCommandBar="true" />
        <appMagic:property name="BorderThickness" showInCommandBar="true" />
        <appMagic:property name="BorderColor" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Radius">
        <appMagic:property name="RadiusTopLeft" />
        <appMagic:property name="RadiusTopRight" />
        <appMagic:property name="RadiusBottomLeft" />
        <appMagic:property name="RadiusBottomRight" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledColor" />
        <appMagic:property name="DisabledFill" />
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverColor" />
        <appMagic:property name="HoverFill" />
        <appMagic:property name="HoverBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="PressedColor">
        <appMagic:property name="PressedColor" />
        <appMagic:property name="PressedFill" />
        <appMagic:property name="PressedBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.0.1">
    <!-- Removed defaultValue for BorderColor. BorderColor default now defined in theme. -->
  </appMagic:conversion>
  <appMagic:conversion from="2.0.1" to="2.1.0">
    <!-- Added a new property VirtualKeyboardMode to hint what type of virtual keyboard for the OS to show-->
    <appMagic:conversionAction type="add" name="VirtualKeyboardMode" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <!-- Added EnableSpellCheck property. -->
    <appMagic:conversionAction type="add" name="EnableSpellCheck" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.2.1">
    <!-- KO template change to use HTML maxlength attribute -->
  </appMagic:conversion>
  <appMagic:conversion from="2.2.1" to="2.3.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.3.1">
    <!-- KO template changes for accessibility fixes -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.1" to="2.3.2">
    <!-- KO template changes for accessibility fixes -->
  </appMagic:conversion>
</widget>
<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/datepicker" name="datepicker" jsClass="AppMagic.Controls.DatePicker" version="2.6.0" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[DATEPICKER
      Control description here.]]></description>
  <requires>
    <require type="css" src="css/datepicker.css" />
    <require type="css" src="/openSource/modified/pikaday/pikaday.css" />
    <require type="javascript" src="/openSource/modified/pikaday/pikaday.js" />
    <require type="javascript" src="js/datepicker.js" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoPointerViewState="true" autoDisabledViewState="true" autoBorders="true" autoFocusedBorders="true" autoFill="true" isVersionFlexible="true" />
  <appMagic:accessibilityChecks controlIsInteractive="true" />
  <content><![CDATA[
      <div
        class="appmagic-datepicker-control"
        data-bind="{
          css: {
            disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled,
            view: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,
            uneditable: !properties.IsEditable()
          }
        }"
      >
        <input
          class="datepicker-textbox"
          type="text"
          data-bind="{
            readOnly: !properties.IsEditable(),
            disable: viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit,
            event: { click: handleClick },
            attr: {
              title: properties.Tooltip() || null,
              'aria-label': properties.AccessibleLabel() || null,
              id: inputId,
              placeholder: properties.InputTextPlaceholder
            },
            style: {
              fontFamily: properties.Font,
              fontSize: properties.Size,
              color: autoProperties.Color,
              fontWeight: properties.FontWeight,
              fontStyle: properties.Italic,
              paddingTop: properties.PaddingTop,
              paddingRight: properties.PaddingRight,
              paddingBottom: properties.PaddingBottom,
              paddingLeft: properties.PaddingLeft
            } }" />
        <div
          class="appmagic-datepicker-icon"
          role="button"
          data-bind="{
            event: { click: handleClick },
            attr: {
              'aria-label': $data.buttonLabel(),
              'aria-controls': popupId,
              'aria-disabled': viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit ? true : null
            },
            style: {
              width: AppMagic.Controls.converters.pxVerticalConverter.view(properties.CalendarPlaceholderWidth()),
              background: properties.IconBackground,
              opacity: (viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled) ? '0.4' : '1',
              color: ($data.iconFillComputed && $data.iconFillComputed()) || properties.IconFill(),
              outlineColor: ($data.iconFillComputed && $data.iconFillComputed()) || properties.IconFill()
            }
          }"
        >
          <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
            data-bind="style: {
              width: AppMagic.Controls.converters.pxHorizontalConverter.view(properties.CalendarPlaceholderWidth()),
              height: AppMagic.Controls.converters.pxVerticalConverter.view(properties.CalendarPlaceholderWidth())
            }"
            focusable="false"
            aria-hidden="true"
            viewBox="-13 -12 52 48">
            <g>
                <path d="M0,24h1h1h22h1h1V0h-1h-1H2H1H0V24z M2,22V8h22v14H2z M24,2v4H2V2H24z"/>
                <rect x="12" y="18" width="2" height="2"/>
                <rect x="6" y="18" width="2" height="2"/>
                <rect x="18" y="14" width="2" height="2"/>
                <rect x="12" y="14" width="2" height="2"/>
                <rect x="6" y="14" width="2" height="2"/>
                <rect x="18" y="10" width="2" height="2"/>
                <rect x="12" y="10" width="2" height="2"/>
            </g>
          </svg>
        </div>
      </div>
    ]]></content>
  <properties>
    <!-- Calendar Data Properties -->
    <property name="SelectedDate" localizedName="##Calendar_SelectedDate##" datatype="date" direction="out" isExpr="true" isPrimaryOutputProperty="true" supportsAutomation="true">
      <title>Selected Date</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##Calendar_SelectedDate_DisplayName##</appMagic:displayName>
    </property>
    <property name="DefaultDate" localizedName="##Calendar_DefaultDate##" datatype="datetime" direction="in" isExpr="true" defaultValue="Today()" isPrimaryInputProperty="true">
      <title>DefaultDate</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##Calendar_DefaultDate_DisplayName##</appMagic:displayName>
    </property>
    <property name="HideCalendar" localizedName="##Hide_Calendar##" datatype="Boolean" hidden="true" defaultValue="true">
      <title>Hide Calendar</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Hide_Calendar_DisplayName##</appMagic:displayName>
    </property>
    <property name="IsEditable" localizedName="##Calendar_IsEditable##" datatype="Boolean" defaultValue="false">
      <title>IsEditable</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_IsEditable_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##Calendar_IsEditable_Tooltip##</appMagic:tooltip>
    </property>
    <property name="DateTimeZone" localizedName="##DatePicker_DateTimeZone##" datatype="DateTimeZone" direction="in" defaultValue="%DateTimeZone.RESERVED%.Local" isExpr="true">
      <title>DateTimeZone</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##DatePicker_DateTimeZone_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##DatePicker_DateTimeZone_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Format" localizedName="##DatePicker_Format##" datatype="string" defaultValue="%DateTimeFormat.RESERVED%.ShortDate" isExpr="true">
      <title>Format</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##DatePicker_Format_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##DatePicker_Format_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Language" localizedName="##DatePicker_Language##" datatype="string" defaultValue="" isExpr="true">
      <title>Language</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##DatePicker_Language_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##DatePicker_Language_Tooltip##</appMagic:tooltip>
    </property>
    <property name="InputTextPlaceholder" localizedName="##DatePicker_InputTextPlaceholder##" datatype="string" defaultValue="If(IsBlank(Self.SelectedDate), Text(Date(2001,12,31), Self.Format, Self.Language))" isExpr="true">
      <title>Input Text Placeholder</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##DatePicker_InputTextPlaceholder_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##DatePicker_InputTextPlaceholder_Tooltip##</appMagic:tooltip>
    </property>
    <!-- If the maximum / minimum values are updated, the constants in DatePicker.ts must to be updated as well. -->
    <property name="StartYear" localizedName="##Calendar_StartYear##" datatype="Number" direction="in" defaultValue="1970" minimum="1000" maximum="9999" isExpr="true">
      <title>Start Year</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##Calendar_StartYear_DisplayName##</appMagic:displayName>
    </property>
    <property name="EndYear" localizedName="##Calendar_EndYear##" datatype="Number" direction="in" defaultValue="2050" minimum="1000" maximum="9999" isExpr="true">
      <title>End Year</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##Calendar_EndYear_DisplayName##</appMagic:displayName>
    </property>
    <property name="StartOfWeek" localizedName="##Calendar_StartOfWeek##" datatype="StartOfWeek" direction="in" defaultValue="%StartOfWeek.RESERVED%.Sunday" isExpr="true">
      <title>Start Of Week</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##Calendar_StartOfWeek_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##Calendar_StartOfWeek_Tooltip##</appMagic:tooltip>
    </property>
    <!-- Calendar Dimensions - Static Hidden properties -->
    <property name="CalendarCellHeight" localizedName="##Calendar_CellHeight##" datatype="Number" hidden="true" defaultValue="42" phoneDefaultValue="66">
      <title>Calendar Cell Height</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_CellHeight_DisplayName##</appMagic:displayName>
    </property>
    <property name="CalendarWidth" localizedName="##Calendar_Width##" datatype="Number" hidden="true" defaultValue="350" phoneDefaultValue="500">
      <title>Calendar Width</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_Width_DisplayName##</appMagic:displayName>
    </property>
    <property name="DateFontSize" localizedName="##Date_FontSize##" datatype="Number" hidden="true" defaultValue="18" phoneDefaultValue="22">
      <title>Date Font Size</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Date_FontSize_DisplayName##</appMagic:displayName>
    </property>
    <property name="CalendarPlaceholderWidth" localizedName="##Calendar_PlaceholderWidth##" datatype="Number" hidden="true" defaultValue="34" phoneDefaultValue="50" webDefaultValue="32">
      <title>Calendar_Placeholde_rWidth</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_PlaceholderWidth_DisplayName##</appMagic:displayName>
    </property>
    <!-- Calendar Colors -->
    <!-- Icon Colors (the little calendar glyph) -->
    <property name="IconFill" localizedName="##Calendar_Icon_Fill##" datatype="Color" isExpr="true" defaultValue="RGBA(255, 255, 255, 1)" converter="argbConverter">
      <title>Icon Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Calendar_Icon_Fill_DisplayName##</appMagic:displayName>
    </property>
    <property name="IconBackground" localizedName="##Calendar_Icon_Background##" datatype="Color" isExpr="true" defaultValue="RGBA(35, 31, 32, 1)" converter="argbConverter">
      <title>Icon Background</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Calendar_Icon_Background_DisplayName##</appMagic:displayName>
    </property>
    <!--Calendar Cell Colors-->
    <property name="SelectedDateFill" localizedName="##Calendar_SelectedDateFill##" datatype="Color" direction="in" hidden="true" isExpr="true" defaultValue="RGBA(37, 70, 148, 1)" converter="argbConverter" styleable="true">
      <title>Selected Date Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_SelectedDateFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="HoverDateFill" localizedName="##Calendar_HoverDateFill##" datatype="Color" direction="in" hidden="true" isExpr="true" defaultValue="RGBA(190, 202, 226, 1)" converter="argbConverter" styleable="true">
      <title>Hover Date Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_HoverDateFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="CurrentDateFill" localizedName="##Calendar_CurrentDateFill##" datatype="Color" direction="in" hidden="true" isExpr="true" defaultValue="RGBA(255, 255, 255, 1)" converter="argbConverter" styleable="true">
      <title>Current Date Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_CurrentDateFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="CalendarHeaderFill" localizedName="##Calendar_HeaderFill##" direction="in" hidden="true" datatype="Color" isExpr="true" defaultValue="RGBA(68, 97, 165, 1)" converter="argbConverter" styleable="true">
      <title>Calendar Header Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_HeaderFill_DisplayName##</appMagic:displayName>
    </property>
    <!--Calendar Font Colors-->
    <property name="MonthColor" localizedName="##Calendar_MonthColor##" datatype="Color" direction="in" hidden="true" isExpr="true" defaultValue="RGBA(255, 255, 255, 1)" converter="argbConverter" styleable="true">
      <title>Month Font Color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_MonthColor_DisplayName##</appMagic:displayName>
    </property>
    <property name="WeekColor" localizedName="##Calendar_WeekColor##" datatype="Color" direction="in" hidden="true" isExpr="true" defaultValue="RGBA(255, 255, 255, 1)" converter="argbConverter" styleable="true">
      <title>Week Font Color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_WeekColor_DisplayName##</appMagic:displayName>
    </property>
    <property name="DayColor" localizedName="##Calendar_DayColor##" datatype="Color" direction="in" hidden="true" isExpr="true" defaultValue="RGBA(255, 255, 255, 1)" converter="argbConverter" styleable="true">
      <title>Day Font Color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Calendar_DayColor_DisplayName##</appMagic:displayName>
    </property>
    <!--Properties needed for gallery mode-->
    <property name="Reset" localizedName="##commonProperties_Reset##" datatype="Boolean" defaultValue="false" direction="in">
      <title>Reset</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Data -->
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="BorderColor" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="Self.Fill" />
    <!-- RDBug 5136801:- Remove the hidden properties -->
    <appMagic:includeProperty name="PressedFill" hidden="true" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="HoverFill" hidden="true" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="PressedBorderColor" hidden="true" defaultValue="Self.BorderColor" />
    <appMagic:includeProperty name="HoverBorderColor" hidden="true" defaultValue="Self.BorderColor" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="RGBA(56, 56, 56, 1)" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" defaultValue="2" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="4" />
    <appMagic:includeProperty name="Fill" defaultValue="RGBA(255, 255, 255, 1)" />
    <appMagic:includeProperty name="Font" />
    <appMagic:includeProperty name="Size" defaultValue="14" phoneDefaultValue="24" />
    <appMagic:includeProperty name="FontWeight" />
    <appMagic:includeProperty name="Italic" />
    <appMagic:includeProperty name="Color" defaultValue="RGBA(70, 68, 64, 1)" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="RGBA(70, 68, 64, 1)" />
    <appMagic:includeProperty name="Width" defaultValue="328" phoneDefaultValue="575" webDefaultValue="180" />
    <appMagic:includeProperty name="Height" defaultValue="40" phoneDefaultValue="70" webDefaultValue="32" />
    <appMagic:includeProperty name="PaddingTop" defaultValue="0" />
    <appMagic:includeProperty name="PaddingRight" defaultValue="5" />
    <appMagic:includeProperty name="PaddingBottom" defaultValue="5" />
    <appMagic:includeProperty name="PaddingLeft" defaultValue="12" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="DisplayMode" />
    <appMagic:includeProperty name="OnSelect" direction="in" />
    <appMagic:includeProperty name="OnChange" direction="in" />
    <appMagic:includeProperty name="TabIndex" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="10" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="30" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" phoneDefaultValue="640" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" phoneDefaultValue="1136" />
  </appMagic:includeProperties>
  <!--Property Dependencies -->
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="DateTimeZone" output="SelectedDate" />
    <appMagic:propertyDependency input="DefaultDate" output="SelectedDate" />
    <appMagic:propertyDependency input="EndYear" output="SelectedDate" />
    <appMagic:propertyDependency input="Reset" output="SelectedDate" />
    <appMagic:propertyDependency input="StartYear" output="SelectedDate" />
  </appMagic:propertyDependencies>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Popular" priority="70" />
    <appMagic:category name="Input" priority="60" />
    <appMagic:category name="ClassicControls" priority="60" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="DateTimeZone" />
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Padding">
        <appMagic:property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <appMagic:property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <appMagic:property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <appMagic:property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Color" showInFloatie="true" showInCommandBar="true" />
        <appMagic:property name="Fill" showInFloatie="true" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:property name="Font" displayType="FontEnum" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="Size" labelOverride="##FontSize_Property##" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="FontWeight" displayType="EnumIcon" itemsOrder="Bold;Semibold;Normal;Lighter" showInCommandBar="true" />
      <appMagic:propertyGroup name="Style">
        <appMagic:property name="Italic" displayType="ToggleButton" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" showInCommandBar="true" />
        <appMagic:property name="BorderThickness" showInCommandBar="true" />
        <appMagic:property name="BorderColor" showInCommandBar="true" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledColor" />
        <appMagic:property name="DisabledFill" />
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.0.1">
    <!-- Removed defaultValue for BorderColor. BorderColor default now defined in theme. -->
  </appMagic:conversion>
  <appMagic:conversion from="2.0.1" to="2.1.0">
    <appMagic:conversionAction type="add" name="IconBackground" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <appMagic:conversionAction type="add" name="InputTextPlaceholder" />
    <!-- Removed direction="in" from Format property. -->
    <!-- Removed direction="in" from Language property. -->
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.3.0">
    <appMagic:conversionAction type="add" name="StartOfWeek" />
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.4.0">
    <!-- Added template property for a11y in non-editable mode -->
    <appMagic:conversionAction type="block" newDocVersion="1.283" />
  </appMagic:conversion>
  <appMagic:conversion from="2.4.0" to="2.5.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.5.0" to="2.6.0">
    <!-- Adding showInCommandBar flag -->
  </appMagic:conversion>
</widget>
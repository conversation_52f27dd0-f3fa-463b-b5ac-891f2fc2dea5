{"ControlStates": {"AbsencesByDate": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Fill", "IsLockable": false, "NameMapSourceSchema": "?"}, "ImagePosition", "Height", "<PERSON><PERSON><PERSON>", "Size", "Orientation", "LoadingSpinner", "LoadingSpinnerColor", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "OnVisible", "IsLockable": false, "NameMapSourceSchema": "?"}], "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "AbsencesByDate", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "User"}, {"Category": "Design", "PropertyName": "ImagePosition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Orientation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnVisible", "RuleProviderType": "User"}], "StyleName": "defaultScreenStyle", "Type": "ControlInfo"}, "Back": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Color", "DisabledColor", "PressedColor", "HoverColor", "DisplayMode", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Fill", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight", "BorderThickness", "FocusedBorderThickness", "Size", "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "false", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, "PaddingLeft", "PaddingBottom"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Back", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultButtonStyle", "Type": "ControlInfo"}, "Classification": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Title", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Classification", "OptimizeForDevices": "Off", "ParentIndex": 7, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "titleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "DatePicker_End": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DefaultDate", "DateTimeZone", "Format", "InputTextPlaceholder", "StartYear", "EndYear", "StartOfWeek", "IconFill", "IconBackground", "SelectedDateFill", "HoverDateFill", "CurrentDateFill", "CalendarHeaderFill", "MonthColor", "WeekColor", "DayColor", "BorderColor", "DisabledFill", "PressedFill", "HoverFill", "PressedBorderColor", "HoverBorderColor", "DisabledBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "Font", "FontWeight", "Color", "DisabledColor", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DatePicker_End", "OptimizeForDevices": "Off", "ParentIndex": 9, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DefaultDate", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DateTimeZone", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Format", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "InputTextPlaceholder", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "EndYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartOfWeek", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectedDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CurrentDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CalendarHeaderFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MonthColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "WeekColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DayColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDatePickerStyle", "Type": "ControlInfo"}, "DatePicker_Start": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DefaultDate", "DateTimeZone", "Format", "InputTextPlaceholder", "StartYear", "EndYear", "StartOfWeek", "IconFill", "IconBackground", "SelectedDateFill", "HoverDateFill", "CurrentDateFill", "CalendarHeaderFill", "MonthColor", "WeekColor", "DayColor", "BorderColor", "DisabledFill", "PressedFill", "HoverFill", "PressedBorderColor", "HoverBorderColor", "DisabledBorderColor", "BorderStyle", "FocusedBorderColor", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Fill", "IsLockable": false, "NameMapSourceSchema": "?"}, "Font", "FontWeight", "Color", "DisabledColor", "<PERSON><PERSON><PERSON>", "Height", "X", "Y", "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DatePicker_Start", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DefaultDate", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DateTimeZone", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Format", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "EndYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartOfWeek", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "InputTextPlaceholder", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectedDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CurrentDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CalendarHeaderFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MonthColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "WeekColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DayColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDatePickerStyle", "Type": "ControlInfo"}, "Dropdown_HourEnd": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "DropDownSample", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "SelectionColor", "ChevronFill", "ChevronHoverFill", "ChevronDisabledFill", "ChevronBackground", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "Color", "HoverColor", "PressedColor", "DisabledColor", "BorderColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Dropdown_HourEnd", "OptimizeForDevices": "Off", "ParentIndex": 8, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "NameMap": "{\"Value\":\"Value\"}", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDropdownStyle", "Type": "ControlInfo"}, "Dropdown_HourStart": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "DropDownSample", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "SelectionColor", "ChevronFill", "ChevronHoverFill", "ChevronDisabledFill", "ChevronBackground", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "Color", "HoverColor", "PressedColor", "DisabledColor", "BorderColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Dropdown_HourStart", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "NameMap": "{\"Value\":\"Value\"}", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDropdownStyle", "Type": "ControlInfo"}, "Dropdown_MinEnd": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "DropDownSample", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "SelectionColor", "ChevronFill", "ChevronHoverFill", "ChevronDisabledFill", "ChevronBackground", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "Color", "HoverColor", "PressedColor", "DisabledColor", "BorderColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Dropdown_MinEnd", "OptimizeForDevices": "Off", "ParentIndex": 10, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "NameMap": "{\"Value\":\"Value\"}", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDropdownStyle", "Type": "ControlInfo"}, "Dropdown_MinStart": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "DropDownSample", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "SelectionColor", "ChevronFill", "ChevronHoverFill", "ChevronDisabledFill", "ChevronBackground", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "Color", "HoverColor", "PressedColor", "DisabledColor", "BorderColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, "PaddingRight", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Dropdown_MinStart", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "NameMap": "{\"Value\":\"Value\"}", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDropdownStyle", "Type": "ControlInfo"}, "Gal_Absences": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "WrapCount", "TemplateSize", "TemplatePadding", "Layout", "Transition", "DelayItemLoading", "LoadingSpinner", "LoadingSpinnerColor", "DisplayMode", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "GalleryTemplateChildName": "galleryTemplate7", "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Gal_Absences", "OptimizeForDevices": "Off", "ParentIndex": 19, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "WrapCount", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplatePadding", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Layout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Transition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DelayItemLoading", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultGalleryStyle", "Type": "ControlInfo"}, "galleryTemplate7": {"AllowAccessToGlobals": true, "ControlPropertyState": ["TemplateFill"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "galleryTemplate7", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "TemplateFill", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "icoSortClassification": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Icon", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Color", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "DisplayMode", "X", "Y", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "FocusedBorderColor", "ZIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "BorderThickness", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FocusedBorderThickness", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "false", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "0", "InvariantPropertyName": "Rotation", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "icoSortClassification", "OptimizeForDevices": "Off", "ParentIndex": 17, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Rotation", "RuleProviderType": "User"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "defaultIconStyle", "Type": "ControlInfo"}, "icoSortRequestTime": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Icon", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Color", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "FocusedBorderColor", "ZIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "BorderThickness", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FocusedBorderThickness", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "false", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "0", "InvariantPropertyName": "Rotation", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "icoSortRequestTime", "OptimizeForDevices": "Off", "ParentIndex": 18, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Rotation", "RuleProviderType": "User"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "defaultIconStyle", "Type": "ControlInfo"}, "Leave_from": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Title", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGallerySubtitle.ID%", "Name": "Leave_from", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "subtitleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "Leave_to": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Title", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingRight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Leave_to", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LeaveType": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Title", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LeaveType", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "titleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "Name": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Title", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryTitle.ID%", "Name": "Name", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "titleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "Rectangle14": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "FocusedBorderColor", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "OnSelect", "ZIndex", "Visible", "BorderThickness", "TabIndex"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryRectangle1.ID%", "Name": "Rectangle14", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "primary2RectangleStyle", "Type": "ControlInfo"}, "RequestedTime": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "\"Text\"", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingRight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "RequestedTime", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "RestrictionAdd": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Color", "DisabledColor", "PressedColor", "HoverColor", "DisplayMode", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight", "BorderThickness", "FocusedBorderThickness", "Size", "Italic", "Underline", "Strikethrough", "OnSelect"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "RestrictionAdd", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultButtonStyle", "Type": "ControlInfo"}, "Separator15": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "FocusedBorderColor", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "OnSelect", "ZIndex", "BorderThickness", "TabIndex"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGallerySeparator1.ID%", "Name": "Separator15", "OptimizeForDevices": "Off", "ParentIndex": 8, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "dividerStyle2020", "Type": "ControlInfo"}, "TextClassification": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FontWeight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Align", "IsLockable": false, "NameMapSourceSchema": "?"}, "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingRight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TextClassification", "OptimizeForDevices": "Off", "ParentIndex": 15, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "TextEnd": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FontWeight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Align", "IsLockable": false, "NameMapSourceSchema": "?"}, "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingRight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TextEnd", "OptimizeForDevices": "Off", "ParentIndex": 13, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "TextFrom": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TextFrom", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "TextLeaveType": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FontWeight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Align", "IsLockable": false, "NameMapSourceSchema": "?"}, "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingRight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TextLeaveType", "OptimizeForDevices": "Off", "ParentIndex": 14, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "TextRequestTime": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FontWeight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Align", "IsLockable": false, "NameMapSourceSchema": "?"}, "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingRight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TextRequestTime", "OptimizeForDevices": "Off", "ParentIndex": 16, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "TextStart": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FontWeight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Align", "IsLockable": false, "NameMapSourceSchema": "?"}, "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingTop", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingRight", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingBottom", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "PaddingLeft", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TextStart", "OptimizeForDevices": "Off", "ParentIndex": 12, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "TexttglShowCanceled": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "FontWeight", "IsLockable": false, "NameMapSourceSchema": "?"}, "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TexttglShowCanceled", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "TextTo": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "TextTo", "OptimizeForDevices": "Off", "ParentIndex": 11, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "tglShowCanceled": {"AllowAccessToGlobals": true, "ControlPropertyState": ["HandleFill", "TextPosition", "FalseFill", "FalseHoverFill", "TrueFill", "TrueHoverFill", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "X", "IsLockable": false, "NameMapSourceSchema": "?"}, "Y", "DisplayMode", "FontWeight", "Color", "DisabledColor", "Font", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "\"On\"", "InvariantPropertyName": "TrueText", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "\"Off\"", "InvariantPropertyName": "FalseText", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "tglShowCanceled", "OptimizeForDevices": "Off", "ParentIndex": 7, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "TrueText", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "FalseText", "RuleProviderType": "User"}, {"Category": "Design", "PropertyName": "HandleFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TextPosition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FalseFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TrueFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TrueHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FalseHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}], "StyleName": "defaultToggleSwitchStyle", "Type": "ControlInfo"}}, "TopParentName": "AbsencesByDate"}
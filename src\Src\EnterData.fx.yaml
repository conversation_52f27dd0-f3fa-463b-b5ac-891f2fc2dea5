"EnterData As screen.'phoneDetailEditLayout_ver3.0'":
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)
    OnVisible: =NewForm(EditForm1)

    RectQuickActionBar3 As rectangle:
        BorderColor: =RGBA(131, 24, 75, 1)
        Fill: =RGBA(211, 66, 9, 1)
        Height: =49.58
        Width: =Parent.Width
        Y: =0.00
        ZIndex: =1

    IconCancel1 As icon.Cancel:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =49.58
        Icon: =Icon.Cancel
        OnSelect: |
            =ResetForm(EditForm1); Reset(ComboBox1);Navigate(
              FirstPage, 
              ScreenTransition.Fade
            )
        PaddingBottom: =10
        PaddingLeft: =12.39
        PaddingRight: =22
        PaddingTop: =10
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Cancel item"
        Width: =88
        Y: =0.00
        ZIndex: =2

    IconAccept1 As icon.Check:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =49.58
        Icon: =Icon.Check
        OnSelect: |
            =With(
                {
                    LeaveFromDateTime: DateTime(
                        Year(DateValue1.SelectedDate), Month(DateValue1.SelectedDate), Day(DateValue1.SelectedDate),
                        Value(HourValue1.Selected.Value), Value(MinuteValue1.Selected.Value), 0
                    ),
                    
                    LeaveToDateTime: DateTime(
                        Year(DateValue2.SelectedDate), Month(DateValue2.SelectedDate), Day(DateValue2.SelectedDate),
                        Value(HourValue2.Selected.Value), Value(MinuteValue2.Selected.Value), 0
                    )
                },
            
                // Check if the 'To' date is actually after the 'From' date
                If(
                    LeaveToDateTime > LeaveFromDateTime,
                    
                    // If TRUE: The dates are valid, so submit the form
                    SubmitForm(EditForm1),
                    
                    // If FALSE: The dates are invalid, so show an error message
                    Notify(
                        "Error: 'Leave To' date must be after 'Leave From' date.",
                        NotificationType.Error, // This makes the banner red
                        3000 // Message disappears after 3 seconds
                    )
                )
            );
        PaddingBottom: =10
        PaddingLeft: =12.39
        PaddingRight: =22
        PaddingTop: =10
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Submit item"
        Width: =88
        X: =Parent.Width - Self.Width
        Y: =0.00
        ZIndex: =3

    LblAppName3 As label:
        Color: =RGBA(255, 255, 255, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Fill: =RGBA(58, 58, 58, 0)
        Height: =49.58
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =15.21
        Text: ="Time Off"
        Width: =Parent.Width - IconCancel1.Width - IconAccept1.Width
        Wrap: =false
        X: =79
        Y: =0.00
        ZIndex: =4

    EditForm1 As form:
        BorderColor: =RGBA(131, 24, 75, 1)
        DataSource: =[@TimeOffIndiana]
        DefaultMode: |
            =//FormMode.Edit
            
            If(
              IsBlank( gblRecordToEdit ),
              FormMode.New,
              FormMode.Edit
            )
        Height: =518.87
        Item: |
            =If(
              EditForm1.Mode = FormMode.New,
              Defaults( TimeOffIndiana ),
             gblRecordToEdit
            )
        NumberOfColumns: =2
        OnSuccess: |
            =Notify("Item saved successfully!", NotificationType.Success);
            
            ResetForm(EditForm1);
            Reset(ComboBox1);
            Notify("Leave request submitted successfully!", NotificationType.Success);
            Navigate(BrowseGallery1, ScreenTransition.Fade)
        Width: =841
        Y: =113.24
        ZIndex: =5

        "'Leave From_DataCard2' As typedDataCard.dateTimeEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="LeaveFrom"
            Default: =ThisItem.'Leave From'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave From')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =28.17
            Required: =true
            Update: =If(Not IsBlank(DateValue1.SelectedDate), DateTime(Year(DateValue1.SelectedDate), Month(DateValue1.SelectedDate), Day(DateValue1.SelectedDate), Value(HourValue1.Selected.Value), Value(MinuteValue1.Selected.Value), 0))
            Width: =420
            X: =0
            Y: =0
            ZIndex: =1

            DataCardKey7 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =0.00
                ZIndex: =1

            DateValue1 As datepicker:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                CalendarHeaderFill: =RGBA(211, 66, 9, 1)
                Color: =RGBA(0, 0, 0, 1)
                DefaultDate: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                EndYear: =Year(Today())+100
                Height: =39.44
                HoverDateFill: =RGBA(255, 211, 205, 1)
                IconBackground: =RGBA(211, 66, 9, 1)
                IsEditable: =true
                PaddingBottom: =0.00
                PaddingLeft: =2.82
                SelectedDateFill: =RGBA(211, 66, 9, 1)
                Size: =13.52
                StartYear: =1899
                Tooltip: =Parent.DisplayName
                Width: =(Parent.Width - 60) / 2
                X: =30
                Y: =29.86
                ZIndex: =2

            HourValue1 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: |-
                    =If(
                        !IsBlank(ComboBox1.Selected.ShiftStart), // Check if a name is selected AND Shift End is not blank
                        Text(Hour(ComboBox1.Selected.ShiftStart), "[$-en-US]00"), // Format the hour as "00", "01", ..., "23"
                        Blank() // Or your preferred fallback default if no name/shift end is selected, e.g., "00" or a specific time like "17"
                    )
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =39.44
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"]
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =DateValue1.Width / 2 - 10
                X: =DateValue1.X + DateValue1.Width + 10
                Y: =29.86
                ZIndex: =3

            Separator2 As label:
                Align: =Align.Center
                BorderColor: =RGBA(131, 24, 75, 1)
                Color: =RGBA(0, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Bold
                Height: =39.44
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =11.83
                Text: |-
                    =":"
                Width: =10
                Wrap: =false
                X: =HourValue1.X + HourValue1.Width
                Y: =29.86
                ZIndex: =4

            MinuteValue1 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: |-
                    =If(
                        !IsBlank(ComboBox1.Selected.ShiftStart), // Check if a name is selected AND Shift End is not blank
                        Text(Minute(ComboBox1.Selected.ShiftStart), "[$-en-US]00"), // Format the hour as "00", "01", ..., "23"
                        Blank() // Or your preferred fallback default if no name/shift end is selected, e.g., "00" or a specific time like "17"
                    )
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =39.44
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"]
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =HourValue1.Width
                X: =HourValue1.X + HourValue1.Width + Separator2.Width
                Y: =29.86
                ZIndex: =5

            ErrorMessage2 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =69.30
                ZIndex: =6

            StarVisible2 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =0.00
                ZIndex: =7

        "'Leave To_DataCard2' As typedDataCard.dateTimeEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="LeaveTo"
            Default: =ThisItem.'Leave To'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave To')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =28.17
            Required: =true
            Update: =If(Not IsBlank(DateValue2.SelectedDate), DateTime(Year(DateValue2.SelectedDate), Month(DateValue2.SelectedDate), Day(DateValue2.SelectedDate), Value(HourValue2.Selected.Value), Value(MinuteValue2.Selected.Value), 0))
            Width: =420
            X: =1
            Y: =0
            ZIndex: =1

            DataCardKey8 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =5.63
                ZIndex: =1

            DateValue2 As datepicker:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                CalendarHeaderFill: =RGBA(211, 66, 9, 1)
                Color: =RGBA(0, 0, 0, 1)
                DefaultDate: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                EndYear: =Year(Today())+100
                Height: =39.44
                HoverDateFill: =RGBA(255, 211, 205, 1)
                IconBackground: =RGBA(211, 66, 9, 1)
                IsEditable: =true
                PaddingBottom: =0.00
                PaddingLeft: =2.82
                SelectedDateFill: =RGBA(211, 66, 9, 1)
                Size: =13.52
                StartYear: =1899
                Tooltip: =Parent.DisplayName
                Width: =(Parent.Width - 60) / 2
                X: =30
                Y: =35.49
                ZIndex: =2

            HourValue2 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: |-
                    =If(
                        !IsBlank(ComboBox1.Selected.ShiftEnd), // Check if a name is selected AND Shift End is not blank
                        Text(Hour(ComboBox1.Selected.ShiftEnd), "[$-en-US]00"), // Format the hour as "00", "01", ..., "23"
                        Blank() // Or your preferred fallback default if no name/shift end is selected, e.g., "00" or a specific time like "17"
                    )
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =39.44
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"]
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =DateValue2.Width / 2 - 10
                X: =DateValue2.X + DateValue2.Width + 10
                Y: =35.49
                ZIndex: =3

            Separator3 As label:
                Align: =Align.Center
                BorderColor: =RGBA(131, 24, 75, 1)
                Color: =RGBA(0, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Bold
                Height: =39.44
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =11.83
                Text: |-
                    =":"
                Width: =10
                Wrap: =false
                X: =HourValue2.X + HourValue2.Width
                Y: =35.49
                ZIndex: =4

            MinuteValue2 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: |-
                    =If(
                        !IsBlank(ComboBox1.Selected.ShiftEnd), // Check if a name is selected AND Shift End is not blank
                        Text(Minute(ComboBox1.Selected.ShiftEnd), "[$-en-US]00"), // Format the hour as "00", "01", ..., "23"
                        Blank() // Or your preferred fallback default if no name/shift end is selected, e.g., "00" or a specific time like "17"
                    )
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =39.44
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"]
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =HourValue2.Width
                X: =HourValue2.X + HourValue2.Width + Separator3.Width
                Y: =35.49
                ZIndex: =5

            ErrorMessage3 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =50
                Y: =74.93
                ZIndex: =6

            StarVisible3 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =5.63
                ZIndex: =7

        Approval_DataCard2 As typedDataCard.toggleEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Approval"
            Default: =ThisItem.Approval
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Approval)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =73
            Required: =true
            Update: =DataCardValue8.Value
            Width: =420
            X: =0
            Y: =1
            ZIndex: =1

            DataCardKey10 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =5.63
                ZIndex: =1

            DataCardValue8 As toggleSwitch:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DisplayMode: =Parent.DisplayMode
                FalseFill: =RGBA(128, 130, 133, 1)
                FalseText: ="No"
                HandleFill: =RGBA(255, 255, 255, 1)
                Height: =27.61
                Size: =11.83
                Tooltip: =Parent.DisplayName
                TrueFill: =RGBA(211, 66, 9, 1)
                TrueText: ="Yes"
                Width: =154
                X: =30
                Y: =35.49
                ZIndex: =2

            ErrorMessage5 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =63.10
                ZIndex: =3

            StarVisible5 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =5.63
                ZIndex: =4

        Scheduled_DataCard2 As typedDataCard.toggleEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Scheduled"
            Default: =ThisItem.Scheduled
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Scheduled)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =73
            Required: =true
            Update: =DataCardValue17.Value
            Width: =420
            X: =1
            Y: =1
            ZIndex: =1

            DataCardKey20 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue17 As toggleSwitch:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DisplayMode: =Parent.DisplayMode
                FalseFill: =RGBA(128, 130, 133, 1)
                FalseText: ="No"
                HandleFill: =RGBA(255, 255, 255, 1)
                Height: =27.61
                Size: =11.83
                Tooltip: =Parent.DisplayName
                TrueFill: =RGBA(211, 66, 9, 1)
                TrueText: ="Yes"
                Width: =154
                X: =30
                Y: =DataCardKey20.Y + DataCardKey20.Height + 5
                ZIndex: =2

            ErrorMessage22 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue17.Y + DataCardValue17.Height
                ZIndex: =3

            StarVisible22 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey20.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey20.Y
                ZIndex: =4

        LeaveTypeDropdown As typedDataCard.comboBoxEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="LeaveType"
            Default: =ThisItem.'Leave Type'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave Type')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =28.17
            Required: =true
            Update: =LeaveTypeDropdownValue.Selected
            Width: =420
            X: =0
            Y: =2
            ZIndex: =1

            DataCardKey6 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =5.63
                ZIndex: =1

            LeaveTypeDropdownValue As combobox:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                DefaultSelectedItems: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayFields: =["Value"]
                DisplayMode: =Parent.DisplayMode
                Height: =39.44
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                InputTextPlaceholder: ="Leave Type"
                Items: =Choices([@TimeOffIndiana].'Leave Type')
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SearchFields: =["Value"]
                SearchItems: =Choices(TimeOffIndiana.'Leave Type',LeaveTypeDropdownValue.SearchText)
                SelectionFill: =RGBA(211, 66, 9, 1)
                SelectMultiple: =false
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =35.49
                ZIndex: =2

            ErrorMessage1 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =74.93
                ZIndex: =3

            StarVisible1 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =5.63
                ZIndex: =4

        canceled_DataCard2 As typedDataCard.toggleEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="canceled"
            Default: =ThisItem.canceled
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,canceled)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =82
            Required: =false
            Update: =DataCardValue21.Value
            Width: =420
            X: =1
            Y: =2
            ZIndex: =1

            DataCardKey29 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue21 As toggleSwitch:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DisplayMode: =Parent.DisplayMode
                FalseFill: =RGBA(128, 130, 133, 1)
                HandleFill: =RGBA(255, 255, 255, 1)
                Height: =27.61
                Size: =11.83
                Tooltip: =Parent.DisplayName
                TrueFill: =RGBA(211, 66, 9, 1)
                Width: =154
                X: =30
                Y: =DataCardKey29.Y + DataCardKey29.Height + 5
                ZIndex: =2

            ErrorMessage23 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue21.Y + DataCardValue21.Height
                ZIndex: =3

            StarVisible23 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey29.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey29.Y
                ZIndex: =4

        "'Requested Time_DataCard1' As typedDataCard.dateTimeEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="createdtime"
            Default: =ThisItem.'Requested Time'
            DisplayMode: =If(LeaveTypeDropdownValue.Selected.Value  ="AWS", DisplayMode.Disabled, DisplayMode.Edit)
            DisplayName: ="Time of Leave Request"
            Fill: =RGBA(0, 0, 0, 0)
            Height: =85
            Required: =true
            Update: =If(Not IsBlank(DateValue3.SelectedDate), DateTime(Year(DateValue3.SelectedDate), Month(DateValue3.SelectedDate), Day(DateValue3.SelectedDate), Value(HourValue3.Selected.Value), Value(MinuteValue3.Selected.Value), 0))
            Width: =420
            X: =0
            Y: =3
            ZIndex: =1

            DataCardKey19 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DateValue3 As datepicker:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                CalendarHeaderFill: =RGBA(211, 66, 9, 1)
                Color: =RGBA(0, 0, 0, 1)
                DefaultDate: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                EndYear: =Year(Today())+100
                Height: =39.44
                HoverDateFill: =RGBA(255, 211, 205, 1)
                IconBackground: =RGBA(211, 66, 9, 1)
                IsEditable: =true
                PaddingBottom: =0
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                SelectedDateFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                StartYear: =1899
                Tooltip: =Parent.DisplayName
                Width: =(Parent.Width - 60) / 2
                X: =30
                Y: =DataCardKey19.Y + DataCardKey19.Height + 5
                ZIndex: =2

            HourValue3 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Hour(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =DateValue3.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =DateValue3.Width / 2 - 10
                X: =DateValue3.X + DateValue3.Width + 10
                Y: =DateValue3.Y
                ZIndex: =3

            Separator4 As label:
                Align: =Align.Center
                BorderColor: =RGBA(131, 24, 75, 1)
                Color: =RGBA(0, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Bold
                Height: =HourValue3.Height
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =21
                Text: |-
                    =":"
                Width: =10
                Wrap: =false
                X: =HourValue3.X + HourValue3.Width
                Y: =HourValue3.Y
                ZIndex: =4

            MinuteValue3 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Minute(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =HourValue3.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =HourValue3.Width
                X: =HourValue3.X + HourValue3.Width + Separator4.Width
                Y: =HourValue3.Y
                ZIndex: =5

            ErrorMessage12 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =HourValue3.Y + HourValue3.Height
                ZIndex: =6

            StarVisible12 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey19.Height
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey19.Y
                ZIndex: =7

        Role_DataCard1 As typedDataCard.textualEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Role"
            Default: =ThisItem.Role
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Role)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =28.17
            MaxLength: =DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Role)
            Required: =false
            Update: =DataCardValue12.Text
            Width: =420
            X: =1
            Y: =3
            ZIndex: =1

            DataCardKey14 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =5.63
                ZIndex: =1

            DataCardValue12 As text:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                BorderThickness: =2
                Color: =RGBA(0, 0, 0, 1)
                Default: =ComboBox1.Selected.field_6
                DelayOutput: =true
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                FocusedBorderThickness: =4
                Height: =39.44
                HoverBorderColor: =RGBA(131, 24, 75, 1)
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                MaxLength: =Parent.MaxLength
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                RadiusBottomLeft: =0
                RadiusBottomRight: =0
                RadiusTopLeft: =0
                RadiusTopRight: =0
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =35.49
                ZIndex: =2

            ErrorMessage9 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =74.93
                ZIndex: =3

            StarVisible9 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =5.63
                ZIndex: =4

        Location_DataCard3 As typedDataCard.textualEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Location"
            Default: ="Customer Service Unit"
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Location)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =28.17
            MaxLength: =DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Location)
            Required: =false
            Update: =DataCardValue11.Text
            Width: =420
            X: =0
            Y: =4
            ZIndex: =1

            DataCardKey13 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =8.45
                ZIndex: =1

            DataCardValue11 As text:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                BorderThickness: =2
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DelayOutput: =true
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                FocusedBorderThickness: =4
                Height: =39.44
                HoverBorderColor: =RGBA(131, 24, 75, 1)
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                MaxLength: =Parent.MaxLength
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                RadiusBottomLeft: =0
                RadiusBottomRight: =0
                RadiusTopLeft: =0
                RadiusTopRight: =0
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =38.31
                ZIndex: =2

            ErrorMessage8 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =77.75
                ZIndex: =3

            StarVisible8 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =8.45
                ZIndex: =4

        "'Created BY_DataCard1' As typedDataCard.comboBoxEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="CreatedBY"
            Default: |
                ={
                  '@odata.type': "#Microsoft.Azure.Connectors.SharePoint.SPListExpandedUser",
                  Claims:      "i:0#.f|membership|" & Lower(User().Email),
                  DisplayName: User().FullName,
                  Email:       User().Email
                }
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Created BY')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =28.17
            Required: =false
            Update: |
                =DataCardValue7.Selected
            Visible: =false
            Width: =420
            X: =1
            Y: =4
            ZIndex: =1

            DataCardKey9 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =5.63
                ZIndex: =1

            DataCardValue7 As combobox:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: |
                    ={
                      '@odata.type': "#Microsoft.Azure.Connectors.SharePoint.SPListExpandedUser",
                      Claims:      "i:0#.f|membership|" & Lower(User().Email),
                      DisplayName: User().FullName,
                      Email:       User().Email
                    }
                DefaultSelectedItems: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayFields: =["Email"]
                DisplayMode: =Parent.DisplayMode
                Height: =39.44
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                IsSearchable: =false
                Items: =User()
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SearchFields: =["Email"]
                SearchItems: =Search(User(),DataCardValue7.SearchText,Email)
                SelectionFill: =RGBA(211, 66, 9, 1)
                SelectMultiple: =false
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Visible: =false
                Width: =Parent.Width - 60
                X: =30
                Y: =35.49
                ZIndex: =2

            ErrorMessage4 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =74.93
                ZIndex: =3

            StarVisible4 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =5.63
                ZIndex: =4

        Supervisor_DataCard1 As typedDataCard.textualEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Supervisor"
            Default: =ThisItem.Supervisor
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Supervisor)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            MaxLength: =DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Supervisor)
            Required: =false
            Update: =DataCardValue16.Text
            Width: =420
            X: =1
            Y: =4
            ZIndex: =1

            DataCardKey18 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue16 As text:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                BorderThickness: =2
                Color: =RGBA(0, 0, 0, 1)
                Default: =ComboBox1.Selected.field_12
                DelayOutput: =true
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                FocusedBorderThickness: =4
                Height: =39.44
                HoverBorderColor: =RGBA(131, 24, 75, 1)
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                MaxLength: =Parent.MaxLength
                PaddingLeft: =5
                RadiusBottomLeft: =0
                RadiusBottomRight: =0
                RadiusTopLeft: =0
                RadiusTopRight: =0
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardKey18.Y + DataCardKey18.Height + 5
                ZIndex: =2

            ErrorMessage11 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue16.Y + DataCardValue16.Height
                ZIndex: =3

            StarVisible11 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey18.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey18.Y
                ZIndex: =4

        Manager_DataCard1 As typedDataCard.textualEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Manager"
            Default: =ThisItem.Manager
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Manager)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            MaxLength: =DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Manager)
            Required: =false
            Update: =DataCardValue15.Text
            Width: =420
            X: =0
            Y: =5
            ZIndex: =1

            DataCardKey17 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue15 As text:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                BorderThickness: =2
                Color: =RGBA(0, 0, 0, 1)
                Default: =ComboBox1.Selected.field_13
                DelayOutput: =true
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                FocusedBorderThickness: =4
                Height: =39.44
                HoverBorderColor: =RGBA(131, 24, 75, 1)
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                MaxLength: =Parent.MaxLength
                PaddingLeft: =5
                RadiusBottomLeft: =0
                RadiusBottomRight: =0
                RadiusTopLeft: =0
                RadiusTopRight: =0
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardKey17.Y + DataCardKey17.Height + 5
                ZIndex: =2

            ErrorMessage10 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue15.Y + DataCardValue15.Height
                ZIndex: =3

            StarVisible10 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey17.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey17.Y
                ZIndex: =4

        Name_DataCard1 As typedDataCard.textualEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Name"
            Default: =ThisItem.Name
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Name)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =28.17
            MaxLength: =DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Name)
            Required: =false
            Update: =ComboBox1.Selected.Title
            Visible: =false
            Width: =420
            X: =0
            Y: =6
            ZIndex: =1

            DataCardKey12 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =5.63
                ZIndex: =1

            DataCardValue10 As text:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                BorderThickness: =2
                Color: =RGBA(0, 0, 0, 1)
                Default: =ComboBox1.Selected.Title
                DelayOutput: =true
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                FocusedBorderThickness: =4
                Height: =39.44
                HoverBorderColor: =RGBA(131, 24, 75, 1)
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                MaxLength: =Parent.MaxLength
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                RadiusBottomLeft: =0
                RadiusBottomRight: =0
                RadiusTopLeft: =0
                RadiusTopRight: =0
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =35.49
                ZIndex: =2

            ErrorMessage7 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =5.63
                Live: =Live.Assertive
                PaddingBottom: =0.00
                PaddingLeft: =0.00
                PaddingRight: =0
                PaddingTop: =0
                Size: =13.52
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =74.93
                ZIndex: =3

            StarVisible7 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =27.04
                PaddingBottom: =2.82
                PaddingLeft: =2.82
                Size: =11.83
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =5.63
                ZIndex: =4

    ComboBox1 As combobox:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderThickness: =1
        ChevronBackground: =RGBA(211, 66, 9, 1)
        ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
        ChevronDisabledFill: =RGBA(244, 244, 244, 1)
        ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        ChevronHoverFill: =RGBA(255, 255, 255, 1)
        DefaultSelectedItems: =[]
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        DisplayFields: =["Title"]
        Height: =39.44
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        InputTextPlaceholder: ="Name"
        Items: =ShowColumns('Site Staff Combiner Indiana',Title,Classification,Manager,Supervisor,'Shift Start','Shift End')
        PaddingBottom: =5.63
        PaddingLeft: =5.63
        PressedColor: =RGBA(255, 255, 255, 1)
        PressedFill: =RGBA(131, 24, 75, 1)
        SearchFields: =["Title"]
        SearchItems: =Search(ShowColumns('Site Staff Combiner Indiana',Title,Classification,Manager,Supervisor,'Shift Start','Shift End'),ComboBox1.SearchText,Title)
        SelectionFill: =RGBA(211, 66, 9, 1)
        SelectMultiple: =false
        Size: =11.83
        Width: =417
        X: =32
        Y: =67.61
        ZIndex: =6

    check_availability As button:
        BorderThickness: =0
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(0, 0, 0, 0.15)
        FontWeight: =FontWeight.Semibold
        Height: =39.44
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Navigate(
              AbsencesByDate, 
              ScreenTransition.Fade
            )
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =11.27
        Text: ="Check Availability"
        Width: =280
        X: =272
        Y: =5.07
        ZIndex: =7

    Label1 As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Bold
        Height: =39
        LineHeight: =2
        Size: =15
        Text: ="Requested Time"
        Width: =280
        X: =841
        Y: =67
        ZIndex: =8

    Label2 As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =158
        Size: =12
        Text: |-
            ="Use the calendar picker for the date, then choose the hour and minute dropdowns.            
             This is the actual block of time the staff has asked you to approve—be sure it’s fully filled in."
        Width: =284
        X: =839
        Y: =101
        ZIndex: =9

    TextInput2 As text:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderStyle: =BorderStyle.None
        BorderThickness: =2
        Color: =RGBA(0, 0, 0, 1)
        Default: ="Approval Toggle"
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        FocusedBorderThickness: =4
        FontWeight: =FontWeight.Bold
        Height: =70
        HoverBorderColor: =RGBA(131, 24, 75, 1)
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Size: =15
        Width: =295
        X: =841
        Y: =242
        ZIndex: =10

    Label4 As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =70
        Size: =12
        Text: ="Switch to Yes if you approve the requested period, or No to reject."
        Width: =295
        X: =841
        Y: =297
        ZIndex: =11

    TextInput3 As text:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderStyle: =BorderStyle.None
        BorderThickness: =2
        Color: =RGBA(0, 0, 0, 1)
        Default: ="Check Availability"
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        FocusedBorderThickness: =4
        FontWeight: =FontWeight.Bold
        Height: =70
        HoverBorderColor: =RGBA(131, 24, 75, 1)
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Size: =15
        Width: =262
        X: =839
        Y: =350
        ZIndex: =12

    Label5 As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =70
        Size: =12
        Text: ="Click Check Availability to surface any team-calendar conflicts before submitting."
        Width: =295
        X: =841
        Y: =410
        ZIndex: =13

    ShiftStart As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderThickness: =1
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =26
        Size: =11.83
        Text: |-
            =If(
                !IsBlank(ComboBox1.Selected),
               ComboBox1.Selected.ShiftStart,
                "" 
            )
        Width: =55
        X: =641
        Y: =81
        ZIndex: =14

    ShiftStartText As text:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderStyle: =BorderStyle.None
        BorderThickness: =2
        Color: =RGBA(131, 24, 75, 1)
        Default: ="Shift Start"
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        FocusedBorderThickness: =4
        Height: =20
        HoverBorderColor: =RGBA(131, 24, 75, 1)
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Size: =11.83
        Width: =93
        X: =622
        Y: =56
        ZIndex: =15

    ShiftEndText As text:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderStyle: =BorderStyle.None
        BorderThickness: =2
        Color: =RGBA(131, 24, 75, 1)
        Default: ="Shift End"
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        FocusedBorderThickness: =4
        Height: =20
        HoverBorderColor: =RGBA(131, 24, 75, 1)
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Size: =11.83
        Width: =93
        X: =736
        Y: =56
        ZIndex: =16

    ShiftEnd As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderThickness: =1
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =26
        Size: =11.83
        Text: |-
            =If(
                !IsBlank(ComboBox1.Selected),
               ComboBox1.Selected.ShiftEnd,
                "" 
            )
        Width: =55
        X: =755
        Y: =81
        ZIndex: =17


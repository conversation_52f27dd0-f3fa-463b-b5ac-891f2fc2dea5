<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/icon" name="icon" jsClass="AppMagic.Controls.Icon" version="2.5.0" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[Icon
      Control description here.]]></description>
  <requires>
    <require type="javascript" src="/ctrllib/icon/js/icon.js" />
    <require type="other" src="/ctrllib/icon/images/ctrllib-symbols.svg" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoFill="true" autoBorders="true" autoFocusedBorders="true" autoPointerViewState="true" autoDisabledViewState="true" isVersionFlexible="true" supportsSetFocus="true" />
  <content><![CDATA[
      <div class="powerapps-icon no-focus-outline" touch-action="pan-x pan-y" data-control-part="icon" data-bind="
        event: {
          click: handleClick
        },
        shortcut: {
          provider: shortcutProvider,
          capture: false
        },
        attr: {
          title: properties.Tooltip() || null,
          role: (properties.TabIndex() < 0) ? (!!properties.AccessibleLabel() && 'img') : 'button',
          'aria-label': properties.AccessibleLabel() || null,
          'aria-disabled': (properties.TabIndex() >= 0) && (viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit),
          'aria-hidden': properties.TabIndex() < 0 && !properties.AccessibleLabel()
        },
        style: {
          cursor: viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit ? 'default' : 'pointer',
          transform: iconRotationComputed()
        }
      ">

        <div class="icon-svg-container" aria-hidden="true" data-bind="
          attr: {
            width: properties.Width,
            height: properties.Height,
            preserveAspectRatio: properties.PreserveAspectRatio() ? 'xMidYMid meet' : 'none',
          },
          style: {
            paddingTop: properties.PaddingTop,
            paddingRight: properties.PaddingRight,
            paddingBottom: properties.PaddingBottom,
            paddingLeft: properties.PaddingLeft
          }
        ">
        </div>

     </div>
    ]]></content>
  <properties>
    <property name="Icon" localizedName="##icon_Icon##" datatype="Icon" defaultValue="%Icon.RESERVED%.ArrowDown" isExpr="true">
      <title>Icon</title>
      <appMagic:autoBind>false</appMagic:autoBind>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##icon_Icon_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##icon_Icon_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Rotation" localizedName="##icon_Rotation##" datatype="Number" defaultValue="0">
      <title>Rotation</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##icon_Rotation_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##icon_Rotation_Tooltip##</appMagic:tooltip>
    </property>
  </properties>
  <appMagic:includeProperties>
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="ContentLanguage" />
    <appMagic:includeProperty name="AutoDisableOnSelect" />
    <appMagic:includeProperty name="Color" defaultValue="RGBA(0, 0, 0, 1)" converter="argbConverter" isPrimaryOutputProperty="true" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="RGBA(119, 119, 119, 1)" />
    <appMagic:includeProperty name="PressedColor" defaultValue="ColorFade(Self.Color, -20%)" />
    <appMagic:includeProperty name="HoverColor" defaultValue="ColorFade(Self.Color, 20%)" />
    <appMagic:includeProperty name="Fill" defaultValue="RGBA(0, 0, 0, 0)" converter="argbConverter" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="PressedFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="HoverFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="DisplayMode" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="64" converter="pxHorizontalConverter" />
    <appMagic:includeProperty name="Height" defaultValue="64" converter="pxVerticalConverter" />
    <appMagic:includeProperty name="PaddingTop" />
    <appMagic:includeProperty name="PaddingRight" />
    <appMagic:includeProperty name="PaddingBottom" />
    <appMagic:includeProperty name="PaddingLeft" />
    <appMagic:includeProperty name="TabIndex" defaultValue="-1" />
    <!-- Borders -->
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderColor" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="RGBA(56, 56, 56, 1)" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="ColorFade(Self.BorderColor, -20%)" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="ColorFade(Self.BorderColor, 20%)" />
    <appMagic:includeProperty name="BorderThickness" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" />
    <!-- Behavior Properties -->
    <appMagic:includeProperty name="OnSelect" direction="in" isPrimaryInputProperty="true" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="1" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="20" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
    <appMagic:includeProperty name="PreserveAspectRatio" defaultValue="true" />
  </appMagic:includeProperties>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Icon" displayType="IconEnum" showInCommandBar="true" />
      <appMagic:property name="Rotation" />
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Padding">
        <appMagic:property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <appMagic:property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <appMagic:property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <appMagic:property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Color" showInFloatie="true" showInCommandBar="true" />
        <appMagic:property name="Fill" showInFloatie="true" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="FocusedBorder">
        <appMagic:property name="FocusedBorderThickness" />
        <appMagic:property name="FocusedBorderColor" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="AutoDisableOnSelect" />
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledColor" />
        <appMagic:property name="DisabledFill" />
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverColor" />
        <appMagic:property name="HoverFill" />
        <appMagic:property name="HoverBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Icons" priority="50" foldVariants="Add|ArrowDown|ArrowLeft|ArrowRight|ArrowUp|ArrowsUpDown|NextArrow|BackArrow|Camera|Cancel|Check|ChevronDown|ChevronLeft|ChevronRight|Computer|Edit|Message|3DPrinting|Document|DockCheckProperties|Folder|Items|Journal|LevelsLayersItems|ListWatchlistRemind|LogJournal|Map|Mobile|Note|People|Person|Phone|PhoneHangUp|PhotosPictures|Pin|Post|RadarActivityMonitor|Tablet|Tag|Tools|ToolsWrench|Trending|TrendingHashtag|TrendingUpwards|ChevronUp|Trash|Search|Filter|Reload|ExpandView|Compose|Crop|AddDocument|AddLibrary|CameraApertureFocus|clearDrawing|Clock|CollapseView|ColorPicker|ComputerDesktop|ComputerSimple|Controller|DetailList|DocumentwithContent|Draw|EditFolders|Erase|Calendar|CalendarBlank|ListScrollEmpty|ListScrollWatchlist|Mail|Send|OptionsList|Airplane|Attachment|Bell|Bus|Cars|Copy|Currency|Database|DocumentPDF|Download|EmojiFrown|EmojiNeutral|EmojiSad|EmojiSmile|ForkKnife|Hamburger|Home|Information|LikeDislike|Lock|Money|More|Newspaper|OfficeBuilding|Save|Settings|ShoppingCart|Vehicles|Waffle|Warning|Waypoint|Weather|Import|AddUser|Alarm|Blocked|Bookmark|Bug|Calculator|Diamond|DockLeft|DockRight|Enhance|Error|Export|Flag|Globe|GlobeNotConnected|GlobeRefresh|GlobeChangesPending|GlobeWarning|GlobeError|HalfFilledCircle|Health|Heart|Help|Hide|History|HorizontalLine|Hospital|Key|Lightbulb|LightningBolt|Link|Manufacture|Medical|Microphone|Notebook|OpenInNewWindow|Phonebook|Print|Publish|QuestionMark|Redo|Reset|Ribbon|Scan|Share|Shirt|Shop|Signal|Support|Sync|Text|ThumbsDown|ThumbsUp|Train|Tray|Undo|Unlock|VerticalLine|Video|View|ZoomIn|ZoomOut|BookmarkFilled|CancelBadge|CheckBadge|Cut|FilterFlat|FilterFlatFilled|Leave|Paste|ThumbsDownFilled|ThumbsUpFilled|EmojiHappy" />
  </appMagic:insertMetadata>
  <!-- Variants for all icons -->
  <appMagic:controlVariants>
    <appMagic:controlVariant name="Add">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Add" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ArrowDown">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ArrowDown" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ArrowLeft">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ArrowLeft" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ArrowRight">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ArrowRight" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ArrowUp">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ArrowUp" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ArrowsUpDown">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Sort" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="NextArrow">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.NextArrow" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="BackArrow">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.BackArrow" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Camera">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Camera" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Cancel">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Cancel" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Check">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Check" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ChevronDown">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ChevronDown" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ChevronLeft">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ChevronLeft" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ChevronRight">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ChevronRight" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Computer">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Laptop" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Edit">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Edit" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Message">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Message" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="3DPrinting">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Printing3D" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Document">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Document" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="DockCheckProperties">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.DockCheckProperties" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Folder">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Folder" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Items">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Items" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Journal">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Journal" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="LevelsLayersItems">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.LevelsLayersItems" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ListWatchlistRemind">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ListWatchlistRemind" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="LogJournal">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.LogJournal" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Map">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Location" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Mobile">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Mobile" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Note">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Note" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="People">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.People" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Person">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Person" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Phone">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Phone" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="PhoneHangUp">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.PhoneHangUp" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="PhotosPictures">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.PhotosPictures" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Pin">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Pin" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Post">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Post" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="RadarActivityMonitor">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.RadarActivityMonitor" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Tablet">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Tablet" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Tag">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Tag" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Tools">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Tools" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ToolsWrench">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ToolsWrench" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Trending">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Trending" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="TrendingHashtag">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.TrendingHashtag" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="TrendingUpwards">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.TrendingUpwards" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ChevronUp">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ChevronUp" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Trash">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Trash" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Search">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Search" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Filter">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Filter" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Reload">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Reload" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ExpandView">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ExpandView" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Compose">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Compose" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Crop">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Crop" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="AddDocument">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.AddDocument" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="AddLibrary">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.AddLibrary" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="CameraApertureFocus">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.CameraAperture" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="clearDrawing">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ClearDrawing" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Clock">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Clock" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="CollapseView">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.CollapseView" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ColorPicker">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ColorPicker" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ComputerDesktop">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ComputerDesktop" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ComputerSimple">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Devices" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Controller">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Controller" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="DetailList">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.DetailList" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="DocumentwithContent">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.DocumentWithContent" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Draw">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Draw" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="EditFolders">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.LineWeight" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Erase">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Erase" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Calendar">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.AddToCalendar" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="CalendarBlank">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.CalendarBlank" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ListScrollEmpty">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ListScrollEmpty" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ListScrollWatchlist">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ListScrollWatchlist" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Mail">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Mail" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Send">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Send" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="OptionsList">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.OptionsList" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Airplane">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Airplane" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Attachment">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.PaperClip" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Bell">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Bell" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Bus">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Bus" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Cars">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Cars" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Copy">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Copy" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Currency">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Currency" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Database">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Database" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="DocumentPDF">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.DocumentPDF" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Download">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Download" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="EmojiFrown">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.EmojiFrown" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="EmojiNeutral">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.EmojiNeutral" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="EmojiSad">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.EmojiSad" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="EmojiSmile">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.EmojiSmile" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ForkKnife">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ForkKnife" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Hamburger">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Hamburger" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Home">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Home" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Information">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Information" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="LikeDislike">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.LikeDislike" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Lock">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Lock" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Money">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Money" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="More">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.More" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="MSOffice">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.MSOffice" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Newspaper">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Newspaper" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="OfficeBuilding">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.OfficeBuilding" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Save">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Save" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Settings">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Settings" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ShoppingCart">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ShoppingCart" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Vehicles">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Transportation" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Waffle">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Waffle" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Warning">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Warning" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Waypoint">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Waypoint" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Weather">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Weather" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Import">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Import" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="AddUser">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.AddUser" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Alarm">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Alarm" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Blocked">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Blocked" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Bookmark">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Bookmark" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Bug">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Bug" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Calculator">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Calculator" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Diamond">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Diamond" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="DockLeft">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.DockLeft" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="DockRight">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.DockRight" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Enhance">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Enhance" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Error">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Error" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Export">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Export" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Flag">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Flag" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Globe">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Globe" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="GlobeNotConnected">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.GlobeNotConnected" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="GlobeRefresh">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.GlobeRefresh" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="GlobeChangesPending">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.GlobeChangesPending" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="GlobeWarning">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.GlobeWarning" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="GlobeError">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.GlobeError" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="HalfFilledCircle">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.HalfFilledCircle" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Health">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Health" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Heart">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Heart" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Help">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Help" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Hide">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Hide" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="History">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.History" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="HorizontalLine">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.HorizontalLine" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Hospital">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Hospital" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Key">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Key" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Lightbulb">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Lightbulb" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="LightningBolt">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.LightningBolt" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Link">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Link" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Manufacture">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Manufacture" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Medical">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Medical" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Microphone">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Microphone" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Notebook">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Notebook" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="OpenInNewWindow">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.OpenInNewWindow" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Phonebook">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Phonebook" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Print">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Print" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Publish">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Publish" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="QuestionMark">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.QuestionMark" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Redo">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Redo" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Reset">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Reset" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Ribbon">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Ribbon" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Scan">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Scan" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Share">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Share" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Shirt">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Shirt" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Shop">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Shop" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Signal">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Signal" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Support">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Support" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Sync">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Sync" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Text">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Text" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ThumbsDown">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ThumbsDown" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ThumbsUp">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ThumbsUp" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Train">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Train" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Tray">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Tray" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Undo">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Undo" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Unlock">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Unlock" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="VerticalLine">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.VerticalLine" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Video">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Video" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="View">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.View" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ZoomIn">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ZoomIn" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ZoomOut">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ZoomOut" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="BookmarkFilled">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.BookmarkFilled" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="CancelBadge">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.CancelBadge" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="CheckBadge">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.CheckBadge" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Cut">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Cut" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="FilterFlat">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.FilterFlat" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="FilterFlatFilled">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.FilterFlatFilled" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Leave">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Leave" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="Paste">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.Paste" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ThumbsDownFilled">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ThumbsDownFilled" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="ThumbsUpFilled">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.ThumbsUpFilled" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="EmojiHappy">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Icon" defaultValue="%Icon.RESERVED%.EmojiHappy" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
  </appMagic:controlVariants>
  <appMagic:conversion from="2.0.0" to="2.1.0">
    <appMagic:conversionAction type="add" name="Rotation" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <!-- Removed SVG sprite from KO template (in <content> node of this file) -->
    <appMagic:conversionAction type="block" newDocVersion="1.282" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.3.0">
    <!-- Changed to access Width and Height on measuredProperties -->
    <appMagic:conversionAction type="block" newDocVersion="1.295" />
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.3.1">
    <!-- Reverted previous change (Width and Height properties now return actual values) -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.1" to="2.3.2">
    <!-- KO template changes for accessibility fixes -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.2" to="2.4.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.4.0" to="2.5.0">
    <!-- Adding showInCommandBar flag -->
  </appMagic:conversion>
</widget>
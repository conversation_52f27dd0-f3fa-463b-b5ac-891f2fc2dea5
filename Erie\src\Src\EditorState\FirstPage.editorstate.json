{"ControlStates": {"Approval_DataCard1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DataField", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayName", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Required", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": true, "NameMapSourceSchema": "?"}, "BorderColor", "BorderStyle", "Fill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "", "Name": "Approval_DataCard1", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataField", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DisplayName", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Required", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultTypedDataCardStyle", "Type": "ControlInfo"}, "BrowseGallery1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "Absense_test", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "WrapCount", "TemplateSize", "TemplatePadding", "Layout", "Transition", "ShowScrollbar", "DelayItemLoading", "LoadingSpinner", "LoadingSpinnerColor", "DisplayMode", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "X", "Y", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "<PERSON><PERSON><PERSON>", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "ZIndex"], "GalleryTemplateChildName": "galleryTemplate1", "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%BrowseContainer.ID%", "Name": "BrowseGallery1", "OptimizeForDevices": "Off", "ParentIndex": 15, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "WrapCount", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplatePadding", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Layout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Transition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DelayItemLoading", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ShowScrollbar", "RuleProviderType": "Unknown"}], "StyleName": "defaultGalleryStyle", "Type": "ControlInfo"}, "Container2": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "BorderStyle", "Fill", "X", "Y", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "500", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, "Height", "DisplayMode", "ChildTabPriority", "LayoutMode", "LayoutDirection", "LayoutAlignItems", "LayoutJustifyContent", "LayoutGap", "LayoutOverflowX", "LayoutOverflowY", "LayoutWrap", "DropShadow", "ZIndex", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Container2", "OptimizeForDevices": "Off", "ParentIndex": 8, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChildTabPriority", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutDirection", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutAlignItems", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutJustifyContent", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutGap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutOverflowX", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutOverflowY", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LayoutWrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DropShadow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}], "StyleName": "defaultGroupContainerStyle", "Type": "ControlInfo"}, "DataCardKey11": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldName.ID%", "Name": "DataCardKey11", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "DataCardKey15": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldName.ID%", "Name": "DataCardKey15", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "DataCardKey16": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldName.ID%", "Name": "DataCardKey16", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "DataCardKey2": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldName.ID%", "Name": "DataCardKey2", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "DataCardKey3": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldName.ID%", "Name": "DataCardKey3", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "DataCardKey4": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%FieldName.ID%", "Name": "DataCardKey4", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "DataCardKey5": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldName.ID%", "Name": "DataCardKey5", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "DataCardValue13": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldValue.ID%", "Name": "DataCardValue13", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "DataCardValue14": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Items", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DefaultSelectedItems", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "SelectMultiple", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "BorderColor", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, "NavigateFields", "Template", "SearchItems", "MoreItemsButtonColor", "<PERSON><PERSON>lay<PERSON><PERSON><PERSON>", "SearchFields", "SelectionColor", "ChevronFill", "ChevronHoverFill", "ChevronDisabledFill", "ChevronBackground", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "SelectionTagFill", "SelectionTagColor", "Color", "HoverColor", "PressedColor", "DisabledColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldValue.ID%", "Name": "DataCardValue14", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "NavigateFields", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SearchItems", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON>lay<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SearchFields", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DefaultSelectedItems", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SelectMultiple", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Template", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MoreItemsButtonColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTagFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTagColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultComboBoxStyle", "Type": "ControlInfo"}, "DataCardValue2": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldValue.ID%", "Name": "DataCardValue2", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "DataCardValue3": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldValue.ID%", "Name": "DataCardValue3", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "DataCardValue4": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%FieldValue.ID%", "Name": "DataCardValue4", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "DataCardValue5": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "BorderColor", "IsLockable": true, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "HandleFill", "TextPosition", "FalseFill", "FalseHoverFill", "TrueFill", "TrueHoverFill", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Height", "<PERSON><PERSON><PERSON>", "X", "Y", "FontWeight", "Color", "DisabledColor", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "ZIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "\"On\"", "InvariantPropertyName": "TrueText", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "\"Off\"", "InvariantPropertyName": "FalseText", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldValue.ID%", "Name": "DataCardValue5", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "TrueText", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "FalseText", "RuleProviderType": "User"}, {"Category": "Design", "PropertyName": "HandleFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TextPosition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FalseFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FalseHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TrueFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TrueHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}], "StyleName": "defaultToggleSwitchStyle", "Type": "ControlInfo"}, "DataCardValue9": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldValue.ID%", "Name": "DataCardValue9", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "DatePicker_EndFirstPage": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DefaultDate", "DateTimeZone", "Format", "InputTextPlaceholder", "StartYear", "EndYear", "StartOfWeek", "IconFill", "IconBackground", "SelectedDateFill", "HoverDateFill", "CurrentDateFill", "CalendarHeaderFill", "MonthColor", "WeekColor", "DayColor", "BorderColor", "DisabledFill", "PressedFill", "HoverFill", "PressedBorderColor", "HoverBorderColor", "DisabledBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "Font", "FontWeight", "Color", "DisabledColor", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DatePicker_EndFirstPage", "OptimizeForDevices": "Off", "ParentIndex": 12, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DefaultDate", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DateTimeZone", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Format", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "EndYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartOfWeek", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "InputTextPlaceholder", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectedDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CurrentDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CalendarHeaderFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MonthColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "WeekColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DayColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDatePickerStyle", "Type": "ControlInfo"}, "DatePicker_StartFirstPage": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DefaultDate", "DateTimeZone", "Format", "InputTextPlaceholder", "StartYear", "EndYear", "StartOfWeek", "IconFill", "IconBackground", "SelectedDateFill", "HoverDateFill", "CurrentDateFill", "CalendarHeaderFill", "MonthColor", "WeekColor", "DayColor", "BorderColor", "DisabledFill", "PressedFill", "HoverFill", "PressedBorderColor", "HoverBorderColor", "DisabledBorderColor", "BorderStyle", "FocusedBorderColor", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Fill", "IsLockable": false, "NameMapSourceSchema": "?"}, "Font", "FontWeight", "Color", "DisabledColor", "<PERSON><PERSON><PERSON>", "Height", "X", "Y", "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", "Size", "Italic", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DatePicker_StartFirstPage", "OptimizeForDevices": "Off", "ParentIndex": 11, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DefaultDate", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DateTimeZone", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Format", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "EndYear", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "StartOfWeek", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "InputTextPlaceholder", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IconBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectedDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CurrentDateFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "CalendarHeaderFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MonthColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "WeekColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DayColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDatePickerStyle", "Type": "ControlInfo"}, "ddApprovalFilter": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "DropDownSample", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "SelectionColor", "ChevronFill", "ChevronHoverFill", "ChevronDisabledFill", "ChevronBackground", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "Color", "HoverColor", "PressedColor", "DisabledColor", "BorderColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "DisplayMode", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "\"1\"", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "dd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OptimizeForDevices": "Off", "ParentIndex": 13, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "NameMap": "{\"Value\":\"Value\"}", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "User"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultDropdownStyle", "Type": "ControlInfo"}, "DetailForm1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "DataSource", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, "Fill", "BorderColor", "BorderStyle", "FocusedBorderColor", "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "49.58", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "<PERSON><PERSON><PERSON>", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, "Height", "ZIndex"], "DynamicProperties": [{"ControlPropertyState": "FillPortions", "Property": {"Category": "Design", "PropertyName": "FillPortions", "RuleProviderType": "Unknown"}, "PropertyName": "FillPortions"}, {"ControlPropertyState": "AlignInContainer", "Property": {"Category": "Design", "PropertyName": "AlignInContainer", "RuleProviderType": "Unknown"}, "PropertyName": "AlignInContainer"}, {"ControlPropertyState": "LayoutMinWidth", "Property": {"Category": "Design", "PropertyName": "LayoutMinWidth", "RuleProviderType": "Unknown"}, "PropertyName": "LayoutMinWidth"}, {"ControlPropertyState": "LayoutMinHeight", "Property": {"Category": "Design", "PropertyName": "LayoutMinHeight", "RuleProviderType": "Unknown"}, "PropertyName": "LayoutMinHeight"}], "HasDynamicProperties": true, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "vertical", "MetaDataIDKey": "", "Name": "DetailForm1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataSource", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "User"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultFormViewerStyle", "Type": "ControlInfo"}, "ErrorMessage6": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Visible", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "AutoHeight", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Size", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%ErrorMessage.ID%", "Name": "ErrorMessage6", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "criticalErrorLabelStyle", "Type": "ControlInfo"}, "FirstPage": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "ImagePosition", "Height", "<PERSON><PERSON><PERSON>", "Size", "Orientation", "LoadingSpinner", "LoadingSpinnerColor"], "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%BrowseScreen.AFD%", "Name": "FirstPage", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ImagePosition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Orientation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}], "StyleName": "defaultScreenStyle", "Type": "ControlInfo"}, "galleryTemplate1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, "TemplateFill"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "galleryTemplate1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "TemplateFill", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "IconDelete": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Icon", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "FocusedBorderColor", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "false", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "IconDelete", "OptimizeForDevices": "Off", "ParentIndex": 9, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "defaultIconStyle", "Type": "ControlInfo"}, "IconEdit": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Icon", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "FocusedBorderColor", "ZIndex", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "false", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "IconEdit", "OptimizeForDevices": "Off", "ParentIndex": 10, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultIconStyle", "Type": "ControlInfo"}, "IconNewItem1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "AccessibleLabel", "IsLockable": false, "NameMapSourceSchema": "?"}, "Icon", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "FocusedBorderColor", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplatePhoneIconNewItem.ID%", "Name": "IconNewItem1", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "User"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "invertedBkgHeaderIconStyle", "Type": "ControlInfo"}, "IconRefresh1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "AccessibleLabel", "IsLockable": false, "NameMapSourceSchema": "?"}, "Icon", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "DisplayMode", "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "FocusedBorderColor", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplatePhoneIconRefresh.ID%", "Name": "IconRefresh1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "invertedBkgHeaderIconStyle", "Type": "ControlInfo"}, "IconSortUpDown1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "AccessibleLabel", "IsLockable": false, "NameMapSourceSchema": "?"}, "Icon", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "DisplayMode", "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "FocusedBorderColor", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplatePhoneIconSortUpDown.ID%", "Name": "IconSortUpDown1", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "invertedBkgHeaderIconStyle", "Type": "ControlInfo"}, "Label7": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Label7", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "Label7_1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Label7_1", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "Label7_2": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "LineHeight", "BorderThickness", "FocusedBorderThickness", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Label7_2", "OptimizeForDevices": "Off", "ParentIndex": 7, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblAppName1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Live", "Overflow", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Size", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex", "PaddingLeft", "PaddingBottom"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplatePhoneLblAppName.ID%", "Name": "LblAppName1", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}], "StyleName": "invertedBkgHeaderLabelStyle", "Type": "ControlInfo"}, "Leave From_DataCard1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DataField", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayName", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Required", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": true, "NameMapSourceSchema": "?"}, "BorderColor", "BorderStyle", "Fill", "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "0", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "", "Name": "Leave From_DataCard1", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataField", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DisplayName", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Required", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultTypedDataCardStyle", "Type": "ControlInfo"}, "Leave To_DataCard1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DataField", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayName", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Required", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": true, "NameMapSourceSchema": "?"}, "BorderColor", "BorderStyle", "Fill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "", "Name": "Leave To_DataCard1", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataField", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DisplayName", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Required", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultTypedDataCardStyle", "Type": "ControlInfo"}, "Leave Type_DataCard2": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DataField", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayName", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Required", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Update", "IsLockable": true, "NameMapSourceSchema": "?"}, "BorderColor", "BorderStyle", "Fill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "", "Name": "Leave Type_DataCard2", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataField", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DisplayName", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Required", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Update", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultTypedDataCardStyle", "Type": "ControlInfo"}, "LeaveFrom": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", "Text", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "TabIndex", "OnSelect", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGallerySubtitle.ID%", "Name": "LeaveFrom", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "subtitleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "LeaveTo": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", "Text", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "TabIndex", "OnSelect", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryBody.ID%", "Name": "LeaveTo", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "contentLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "Location_DataCard1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DataField", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayName", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Required", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": true, "NameMapSourceSchema": "?"}, "BorderColor", "BorderStyle", "Fill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Location_DataCard1", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataField", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DisplayName", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Required", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultTypedDataCardStyle", "Type": "ControlInfo"}, "Name_DataCard2": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DataField", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayName", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Required", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": true, "NameMapSourceSchema": "?"}, "BorderColor", "BorderStyle", "Fill", "X", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "4", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "", "Name": "Name_DataCard2", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataField", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DisplayName", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Required", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultTypedDataCardStyle", "Type": "ControlInfo"}, "NextArrow1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Icon", "AccessibleLabel", "<PERSON><PERSON><PERSON>", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "OnSelect", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryNextIcon.ID%", "Name": "NextArrow1", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultIconStyle", "Type": "ControlInfo"}, "Rectangle1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "Visible", "DisplayMode", "TabIndex", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "Select(Parent)", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryRectangle1.ID%", "Name": "Rectangle1", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "primary2RectangleStyle", "Type": "ControlInfo"}, "Rectangle11": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "FocusedBorderColor", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "true", "InvariantPropertyName": "Visible", "IsLockable": false, "NameMapSourceSchema": "?"}], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplatePhoneRectangle1.ID%", "Name": "Rectangle11", "OptimizeForDevices": "Off", "ParentIndex": 14, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "User"}], "StyleName": "primary2RectangleStyle", "Type": "ControlInfo"}, "RectQuickActionBar1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "FocusedBorderColor", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplatePhoneRectQuickActionBar.ID%", "Name": "RectQuickActionBar1", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "invertedBackgroundRectangleStyle", "Type": "ControlInfo"}, "Role_DataCard2": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DataField", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayName", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Required", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": true, "NameMapSourceSchema": "?"}, "BorderColor", "BorderStyle", "Fill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "", "Name": "Role_DataCard2", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "DataField", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DisplayName", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Required", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultTypedDataCardStyle", "Type": "ControlInfo"}, "Separator1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "DisplayMode", "TabIndex", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "OnSelect", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGallerySeparator1.ID%", "Name": "Separator1", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "dividerStyle2020", "Type": "ControlInfo"}, "StarVisible6": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": true, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": true, "AutoRuleBindingString": "", "InvariantPropertyName": "Visible", "IsLockable": true, "NameMapSourceSchema": "?"}, "Live", "LineHeight", "Overflow", "Wrap", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Size", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": true, "LayoutName": "", "MetaDataIDKey": "%FieldRequired.ID%", "Name": "StarVisible6", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}], "StyleName": "accentLabelStyle", "Type": "ControlInfo"}, "Title1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Title", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Font", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Size", "IsLockable": false, "NameMapSourceSchema": "?"}, "FontWeight", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "TabIndex", "OnSelect", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": true, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": true, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryTitle.ID%", "Name": "Title1", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "titleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}}, "TopParentName": "FirstPage"}
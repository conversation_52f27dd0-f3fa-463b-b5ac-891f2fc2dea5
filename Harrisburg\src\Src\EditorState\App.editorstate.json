{"ControlStates": {"App": {"AllowAccessToGlobals": true, "ControlPropertyState": ["MinScreenHeight", "MinScreenWidth", "ConfirmExit", "SizeBreakpoints", "BackEnabled", "Theme", "OnStart"], "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "App", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "ConfirmExit", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "BackEnabled", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MinScreenHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MinScreenWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Theme", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnStart", "RuleProviderType": "User"}, {"Category": "ConstantData", "PropertyName": "SizeBreakpoints", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "Host": {"AllowAccessToGlobals": true, "ControlPropertyState": ["OnNew", "OnEdit", "OnView", "OnSave", "OnCancel"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Host", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Behavior", "PropertyName": "OnNew", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnEdit", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnView", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSave", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnCancel", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}}, "TopParentName": "App"}
<openAjax:widget spec="1.0" id="http://microsoft.com/appmagic/groupContainer" name="groupContainer" jsClass="AppMagic.Controls.GroupContainer.GroupControl" version="1.3.0" styleable="true" xmlns="http://schemas.microsoft.com/appMagic" xmlns:openAjax="http://openajax.org/metadata">
  <openAjax:author name="Microsoft AppMagic" />
  <openAjax:license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></openAjax:license>
  <openAjax:requires>
    <openAjax:require type="css" src="css/groupContainer.css" />
    <openAjax:require type="javascript" src="/ctrllib/common/js/container.js" />
    <openAjax:require type="javascript" src="js/groupContainer.js" />
  </openAjax:requires>
  <capabilities supportsNestedControls="true" supportsNestedComponents="true" replicatesNestedControls="false" contextualViewsEnabled="false" autoBorders="true" autoFill="true" autoPointerViewState="false" autoDisabledViewState="false" isVersionFlexible="true" autoBorderRadius="true" autoDropShadow="true" />
  <includeProperties>
    <includeProperty name="ContentLanguage" />
    <includeProperty name="BorderColor" />
    <includeProperty name="BorderStyle" />
    <includeProperty name="BorderThickness" />
    <includeProperty name="RadiusTopLeft" defaultValue="0" />
    <includeProperty name="RadiusTopRight" defaultValue="0" />
    <includeProperty name="RadiusBottomLeft" defaultValue="0" />
    <includeProperty name="RadiusBottomRight" defaultValue="0" />
    <includeProperty name="Fill" />
    <includeProperty name="X" />
    <includeProperty name="Y" />
    <includeProperty name="Width" defaultValue="500" />
    <includeProperty name="Height" defaultValue="200" phoneDefaultValue="225" />
    <includeProperty name="Visible" />
    <includeProperty name="DisplayMode" hidden="true" />
    <includeProperty name="ChildTabPriority" />
    <includeProperty name="EnableChildFocus" />
    <!-- AutoLayout properties -->
    <includeProperty name="LayoutMode" />
    <includeProperty name="LayoutDirection" />
    <includeProperty name="LayoutAlignItems" />
    <includeProperty name="LayoutJustifyContent" />
    <includeProperty name="LayoutGap" />
    <includeProperty name="LayoutOverflowX" />
    <includeProperty name="LayoutOverflowY" />
    <includeProperty name="LayoutWrap" />
    <includeProperty name="PaddingTop" defaultValue="0" />
    <includeProperty name="PaddingRight" defaultValue="0" />
    <includeProperty name="PaddingBottom" defaultValue="0" />
    <includeProperty name="PaddingLeft" defaultValue="0" />
    <!-- Hidden properties -->
    <includeProperty name="minimumWidth" defaultValue="20" />
    <includeProperty name="minimumHeight" defaultValue="20" />
    <includeProperty name="maximumWidth" defaultValue="1366" phoneDefaultValue="640" />
    <includeProperty name="maximumHeight" defaultValue="7680" phoneDefaultValue="11360" />
    <includeProperty name="DropShadow" defaultValue="%DropShadow.RESERVED%.None" />
  </includeProperties>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <displayMetadata>
    <section>
      <property name="LayoutMode" />
      <property name="LayoutDirection" displayType="EnumIcon" />
      <property name="LayoutJustifyContent" displayType="EnumButtons" itemsOrder="Start;Center;End;SpaceBetween" />
      <property name="LayoutAlignItems" displayType="EnumButtons" itemsOrder="Start;Center;End;Stretch" />
      <property name="LayoutGap" />
      <property name="LayoutOverflowX" />
      <property name="LayoutOverflowY" />
      <property name="LayoutWrap" />
    </section>
    <section>
      <propertyGroup name="Position">
        <property name="X" />
        <property name="Y" />
      </propertyGroup>
      <propertyGroup name="Size">
        <property name="Width" />
        <property name="Height" />
      </propertyGroup>
      <propertyGroup name="Padding">
        <property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </propertyGroup>
    </section>
    <section>
      <propertyGroup name="Color">
        <property name="Fill" />
      </propertyGroup>
      <propertyGroup name="Border">
        <property name="BorderStyle" />
        <property name="BorderThickness" />
        <property name="BorderColor" />
      </propertyGroup>
      <propertyGroup name="Radius">
        <property name="RadiusTopLeft" />
        <property name="RadiusTopRight" />
        <property name="RadiusBottomLeft" />
        <property name="RadiusBottomRight" />
      </propertyGroup>
      <property name="DropShadow" itemsOrder="None;Light;Semilight;Regular;Semibold;Bold;ExtraBold" />
    </section>
    <section>
      <property name="Visible" />
    </section>
    <section>
      <property name="ChildTabPriority" />
      <property name="EnableChildFocus" />
    </section>
  </displayMetadata>
  <controlVariants>
    <controlVariant name="horizontalAutoLayoutContainer">
      <insertMetadata isDeviceOptimized="true">
        <category name="Layout" priority="100" />
      </insertMetadata>
      <overrideProperties>
        <overrideProperty name="LayoutMode" defaultValue="%LayoutMode.RESERVED%.Auto" isExpr="true" />
        <overrideProperty name="LayoutDirection" hidden="false" defaultValue="%LayoutDirection.RESERVED%.Horizontal" isExpr="true" />
        <overrideProperty name="LayoutAlignItems" hidden="false" />
        <overrideProperty name="LayoutJustifyContent" hidden="false" />
        <overrideProperty name="LayoutGap" hidden="false" />
        <overrideProperty name="LayoutOverflowX" hidden="false" />
        <overrideProperty name="LayoutOverflowY" hidden="false" />
        <overrideProperty name="LayoutWrap" hidden="false" defaultValue="false" />
        <!-- Behavior of ChildTabPriority is always true in a layout container, so hide it -->
        <overrideProperty name="ChildTabPriority" hidden="true" defaultValue="true" />
      </overrideProperties>
    </controlVariant>
    <controlVariant name="verticalAutoLayoutContainer">
      <insertMetadata isDeviceOptimized="true">
        <category name="Layout" priority="101" />
      </insertMetadata>
      <overrideProperties>
        <overrideProperty name="LayoutMode" defaultValue="%LayoutMode.RESERVED%.Auto" isExpr="true" />
        <overrideProperty name="LayoutDirection" hidden="false" defaultValue="%LayoutDirection.RESERVED%.Vertical" isExpr="true" />
        <overrideProperty name="LayoutAlignItems" hidden="false" />
        <overrideProperty name="LayoutJustifyContent" hidden="false" />
        <overrideProperty name="LayoutGap" hidden="false" />
        <overrideProperty name="LayoutOverflowX" hidden="false" />
        <overrideProperty name="LayoutOverflowY" hidden="false" />
        <overrideProperty name="LayoutWrap" hidden="false" defaultValue="false" />
        <!-- Behavior of ChildTabPriority is always true in a layout container, so hide it -->
        <overrideProperty name="ChildTabPriority" hidden="true" defaultValue="true" />
      </overrideProperties>
    </controlVariant>
    <controlVariant name="manualLayoutContainer">
      <insertMetadata isDeviceOptimized="true">
        <category name="Layout" priority="102" />
      </insertMetadata>
      <overrideProperties>
        <overrideProperty name="LayoutMode" defaultValue="%LayoutMode.RESERVED%.Manual" isExpr="true" />
        <overrideProperty name="LayoutDirection" hidden="true" />
        <overrideProperty name="LayoutAlignItems" hidden="true" />
        <overrideProperty name="LayoutJustifyContent" hidden="true" />
        <overrideProperty name="LayoutGap" hidden="true" />
        <overrideProperty name="LayoutOverflowX" hidden="true" />
        <overrideProperty name="LayoutOverflowY" hidden="true" />
        <overrideProperty name="LayoutWrap" hidden="true" />
      </overrideProperties>
    </controlVariant>
  </controlVariants>
  <conversion from="1.0.1" to="1.1.0">
    <conversionAction type="add" name="ChildTabPriority" />
    <conversionAction type="add" name="EnableChildFocus" />
  </conversion>
  <conversion from="1.1.0" to="1.2.0">
    <conversionAction type="add" name="RadiusTopLeft" />
    <conversionAction type="add" name="RadiusTopRight" />
    <conversionAction type="add" name="RadiusBottomLeft" />
    <conversionAction type="add" name="RadiusBottomRight" />
  </conversion>
  <conversion from="1.2.0" to="1.3.0">
    <conversionAction type="add" name="DropShadow" />
  </conversion>
</openAjax:widget>
<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/label" name="label" jsClass="AppMagic.Controls.Label" version="2.5.1" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[LABEL
      Control description here.]]></description>
  <requires>
    <require type="css" src="css/label.css" />
    <require type="javascript" src="js/label.js" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" allowsPerCharacterFormatting="true" autoBorders="true" autoFill="true" autoFocusedBorders="true" autoPointerViewState="true" autoDisabledViewState="true" screenActiveAware="true" isVersionFlexible="true" supportsSetFocus="true" />
  <content><![CDATA[
  <div
    class="appmagic-label no-focus-outline"
    touch-action="pan-x pan-y"
    tabIndex="-1"
    data-bind="
      style: {
        fontFamily: properties.Font,
        fontSize: properties.Size,
        color: autoProperties.Color,
        fontWeight: properties.FontWeight,
        fontStyle: properties.Italic,
        textAlign: properties.Align,
        paddingTop: properties.PaddingTop,
        paddingRight: properties.PaddingRight,
        paddingBottom: properties.PaddingBottom,
        paddingLeft: properties.PaddingLeft,
        lineHeight: properties.LineHeight,
        overflowY: properties.Overflow,
        display:  properties.Overflow() === 'auto' ? 'block' : ''
      },
      css: {
        top: properties.AutoHeight() || properties.VerticalAlign() === 'top',
        middle: !properties.AutoHeight() && properties.VerticalAlign() === 'middle',
        bottom: !properties.AutoHeight() && properties.VerticalAlign() === 'bottom',
        disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled,
        readOnly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,
        underline: properties.Underline,
        strikethrough: properties.Strikethrough
      },
      attr: {
        title: properties.Tooltip,
        role: properties.TabIndex() >= 0 ? 'button' : 'presentation',
        'aria-disabled': properties.TabIndex() >= 0 && viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit
      },
      event: {
        click: handleClick
      },
      shortcut: {
        provider: shortcutProvider,
        enabled: shortcutsEnabled
      }"
    >
    <!-- ko if: properties.Role() !== 'heading1' && properties.Role() !== 'heading2' && properties.Role() !== 'heading3' && properties.Role() !== 'heading4' -->
        <div
          spellcheck="false"
          unselectable="off"
          class="appmagic-label-text"
          data-control-part="text"
          data-bind="{
            inlineEditText: properties.Text,
            css: {
              'appmagic-label-single-line': !properties.Wrap()
            },
            attr: {
              'aria-live': live,
              'aria-atomic': live() ? 'true' : null
            }
          }">
        </div>
    <!-- /ko -->
    <!-- ko if: properties.Role() === 'heading1' -->
        <h1
          spellcheck="false"
          unselectable="off"
          class="appmagic-label-text"
          data-control-part="text"
          data-bind="{
            inlineEditText: properties.Text,
            css: {
              'appmagic-label-single-line': !properties.Wrap()
            },
            attr: {
              'aria-live': live,
              'aria-atomic': live() ? 'true' : null
            }
          }">
         </h1>
    <!-- /ko -->
    <!-- ko if: properties.Role() === 'heading2' -->
        <h2
          spellcheck="false"
          unselectable="off"
          class="appmagic-label-text"
          data-control-part="text"
          data-bind="{
            inlineEditText: properties.Text,
            css: {
              'appmagic-label-single-line': !properties.Wrap()
            },
            attr: {
              'aria-live': live,
              'aria-atomic': live() ? 'true' : null
            }
          }">
         </h2>
    <!-- /ko -->
    <!-- ko if: properties.Role() === 'heading3' -->
        <h3
          spellcheck="false"
          unselectable="off"
          class="appmagic-label-text"
          data-control-part="text"
          data-bind="{
            inlineEditText: properties.Text,
            css: {
              'appmagic-label-single-line': !properties.Wrap()
            },
            attr: {
              'aria-live': live,
              'aria-atomic': live() ? 'true' : null
            }
          }">
         </h3>
    <!-- /ko -->
    <!-- ko if: properties.Role() === 'heading4' -->
        <h4
          spellcheck="false"
          unselectable="off"
          class="appmagic-label-text"
          data-control-part="text"
          data-bind="{
            inlineEditText: properties.Text,
            css: {
              'appmagic-label-single-line': !properties.Wrap()
            },
            attr: {
              'aria-live': live,
              'aria-atomic': live() ? 'true' : null
            }
          }">
         </h4>
    <!-- /ko -->
  </div>
  ]]></content>
  <properties>
    <!-- Data -->
    <property name="Live" localizedName="##label_Live##" datatype="Live" defaultValue="%Live.RESERVED%.Off" isExpr="true">
      <title>Live</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##label_Live_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##label_Live_Tooltip##</appMagic:tooltip>
    </property>
    <!-- Design -->
    <property name="LineHeight" localizedName="##label_LineHeight##" datatype="Number" defaultValue="1.2">
      <title>Line Height</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##label_LineHeight_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##label_LineHeight_Tooltip##</appMagic:tooltip>
      <appMagic:helperUI>lineWidth</appMagic:helperUI>
    </property>
    <property name="Overflow" localizedName="##label_Overflow##" datatype="Overflow" defaultValue="%Overflow.RESERVED%.Hidden" isExpr="true" converter="overflowConverter">
      <title>Overflow</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##label_Overflow_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##label_Overflow_Tooltip##</appMagic:tooltip>
      <appMagic:helperUI>overflow</appMagic:helperUI>
    </property>
    <!-- This is not an 'Auto' property. This is for allowing the label to grow vertically to display the content -->
    <property name="AutoHeight" localizedName="##CommonProperties_AutoHeight##" datatype="Boolean" defaultValue="false">
      <title>AutoHeight</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##CommonProperties_AutoHeight_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##CommonProperties_AutoHeight_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Wrap" localizedName="##label_Wrap##" datatype="Boolean" defaultValue="true" canBeCompressed="false">
      <title>Wrap</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##label_Wrap_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##label_Wrap_Tooltip##</appMagic:tooltip>
    </property>
    <!-- IsErrorMessage is deprecated. Delete once a document converter is written to migrate IsErrorMessage=true -> Live=Live.Assertive -->
    <property name="IsErrorMessage" localizedName="##label_IsErrorMessage##" datatype="Boolean" defaultValue="false" hidden="true">
      <appMagic:category>design</appMagic:category>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Data -->
    <appMagic:includeProperty name="Text" defaultValue="##Label_DefaultValue_Text##" isExpr="true" isPrimaryInputProperty="true" isPrimaryOutputProperty="true">
      <appMagic:autoBind>true</appMagic:autoBind>
    </appMagic:includeProperty>
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="Role" />
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="Color" allowsPerCharacterFormatting="true" defaultValue="RGBA(71, 69, 64, 1)" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="PressedColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="HoverColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="BorderColor" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="RGBA(56, 56, 56, 1)" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="Self.BorderColor" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="Self.BorderColor" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" defaultValue="0" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="0" />
    <appMagic:includeProperty name="Fill" allowsPerCharacterFormatting="true" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="RGBA(0, 0, 0, 0)" />
    <appMagic:includeProperty name="PressedFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="HoverFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="Font" allowsPerCharacterFormatting="true" />
    <appMagic:includeProperty name="Size" allowsPerCharacterFormatting="true" defaultValue="14" phoneDefaultValue="24" />
    <appMagic:includeProperty name="FontWeight" allowsPerCharacterFormatting="true" />
    <appMagic:includeProperty name="Italic" allowsPerCharacterFormatting="true" />
    <appMagic:includeProperty name="Underline" allowsPerCharacterFormatting="true" />
    <appMagic:includeProperty name="Strikethrough" allowsPerCharacterFormatting="true" />
    <appMagic:includeProperty name="PaddingTop" defaultValue="5" />
    <appMagic:includeProperty name="PaddingRight" defaultValue="5" />
    <appMagic:includeProperty name="PaddingBottom" defaultValue="5" />
    <appMagic:includeProperty name="PaddingLeft" defaultValue="5" />
    <appMagic:includeProperty name="Align" />
    <appMagic:includeProperty name="VerticalAlign" defaultValue="%VerticalAlign.RESERVED%.Middle" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="150" phoneDefaultValue="560" webDefaultValue="260" />
    <appMagic:includeProperty name="Height" defaultValue="40" phoneDefaultValue="70" webDefaultValue="40" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="DisplayMode" />
    <!-- TabIndex should be -1 to *prevent* making the control a tabstop unless explicitly changed -->
    <appMagic:includeProperty name="TabIndex" defaultValue="-1" />
    <!-- Behavior Properties -->
    <!-- TASK: 85476: Do Behavior properties make sense as input only? -->
    <appMagic:includeProperty name="OnSelect" direction="in" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="1" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="1" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="7680" />
  </appMagic:includeProperties>
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="AutoHeight" output="Height" />
  </appMagic:propertyDependencies>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Popular" priority="10" />
    <appMagic:category name="Display" priority="10" />
    <appMagic:category name="ClassicControls" priority="10" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Text" />
      <appMagic:property name="Font" displayType="FontEnum" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="Size" labelOverride="##FontSize_Property##" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="FontWeight" displayType="EnumIcon" itemsOrder="Bold;Semibold;Normal;Lighter" showInCommandBar="true" />
      <appMagic:propertyGroup name="Style">
        <appMagic:property name="Italic" displayType="ToggleButton" />
        <appMagic:property name="Underline" displayType="ToggleButton" />
        <appMagic:property name="Strikethrough" displayType="ToggleButton" />
      </appMagic:propertyGroup>
      <appMagic:property name="Align" displayType="EnumButtons" itemsOrder="Left;Center;Right;Justify" labelOverride="##FontAlign_Property##" showInFloatie="true" showInCommandBar="true" floatieDisplayType="FaceplateIconEnum" />
      <appMagic:property name="AutoHeight" />
      <appMagic:property name="LineHeight" />
      <appMagic:property name="Overflow" />
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Padding">
        <appMagic:property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <appMagic:property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <appMagic:property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <appMagic:property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Color" showInFloatie="true" showInCommandBar="true" />
        <appMagic:property name="Fill" showInFloatie="true" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="FocusedBorder">
        <appMagic:property name="FocusedBorderThickness" />
        <appMagic:property name="FocusedBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Wrap" />
      <appMagic:property name="VerticalAlign" displayType="EnumIcon" itemsOrder="Top;Middle;Bottom" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledColor" />
        <appMagic:property name="DisabledFill" />
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverColor" />
        <appMagic:property name="HoverFill" />
        <appMagic:property name="HoverBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.1.0">
    <appMagic:conversionAction type="add" name="IsErrorMessage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <appMagic:conversionAction type="add" name="Live" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.3.0">
    <appMagic:conversionAction type="add" name="Role" />
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.3.1">
    <!-- updated template for auto-height -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.1" to="2.3.2">
    <!-- KO template changes for accessibility fixes -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.2" to="2.4.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.4.0" to="2.5.0">
    <!-- Adding showInCommandBar flag -->
  </appMagic:conversion>
  <appMagic:conversion from="2.5.0" to="2.5.1">
    <!-- Adding role=presentation for TabIndex > -1 -->
  </appMagic:conversion>
</widget>
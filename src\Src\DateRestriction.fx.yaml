DateRestriction As screen:
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)

    BackToAbsences As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(56, 96, 178, 1)
        FontWeight: =FontWeight.Semibold
        Height: =22.54
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Navigate(
              AbsencesByDate,
              ScreenTransition.Fade
            )
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =13.52
        Text: ="Back"
        Width: =180
        Y: =0.00
        ZIndex: =2

    LblAddRestriction As label:
        Color: =RGBA(211, 66, 9, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Bold
        Size: =20
        Text: ="Add New Restriction"
        Width: =400
        X: =600
        Y: =40
        ZIndex: =3

    LblEventsLabel As label:
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =30
        Text: |-
            ="Events:"
        Width: =100
        X: =600
        Y: =100
        ZIndex: =4

    ComboEvents As combobox:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(131, 24, 75, 1)
        DisplayFields: =["Value"]
        Items: =Choices(Calender_Restriction.Events)
        SearchFields: =["Value"]
        SearchItems: =Choices(Calender_Restriction.Events,ComboEvents.SearchText)
        SelectMultiple: =false
        Width: =300
        X: =600
        Y: =130
        ZIndex: =5

    LblDateLabel As label:
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =30
        Text: |-
            ="Date:"
        Width: =100
        X: =600
        Y: =190
        ZIndex: =6

    DatePickerRestriction As datepicker:
        BorderColor: =RGBA(131, 24, 75, 1)
        DisabledBorderColor: =RGBA(0, 0, 0, 0)
        DisabledColor: =RGBA(0, 0, 0, 0)
        DisabledFill: =RGBA(0, 0, 0, 0)
        Width: =300
        X: =600
        Y: =220
        ZIndex: =7

    LblTypeLabel As label:
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =30
        Text: |-
            ="Type of Event:"
        X: =600
        Y: =280
        ZIndex: =8

    ComboTypeOfEvent As combobox:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(131, 24, 75, 1)
        DisplayFields: =["Value"]
        Items: =Choices(Calender_Restriction.'Type of Event')
        SearchFields: =["Value"]
        SearchItems: =Choices(Calender_Restriction.'Type of Event',ComboTypeOfEvent.SearchText)
        SelectMultiple: =false
        Width: =300
        X: =600
        Y: =310
        ZIndex: =9

    BtnSaveRestriction As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(211, 66, 9, 1)
        FontWeight: =FontWeight.Semibold
        Height: =45
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Patch(
                Calender_Restriction,
                Defaults(Calender_Restriction),
                {
                    Events: ComboEvents.Selected,
                    Date: DatePickerRestriction.SelectedDate,
                    TypeofEvent: ComboTypeOfEvent.Selected
                }
            );
            Reset(ComboEvents);
            Reset(DatePickerRestriction);
            Reset(ComboTypeOfEvent);
            Notify("Restriction added successfully!", NotificationType.Success)
        Size: =16
        Text: ="Save Restriction"
        Width: =200
        X: =600
        Y: =380
        ZIndex: =10

    BtnCancelRestriction As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(150, 150, 150, 1)
        FontWeight: =FontWeight.Semibold
        Height: =45
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(150, 150, 150, 1), -20%)
        OnSelect: |
            =Reset(ComboEvents);
            Reset(DatePickerRestriction);
            Reset(ComboTypeOfEvent)
        Size: =16
        Text: ="Clear Form"
        Width: =150
        X: =820
        Y: =380
        ZIndex: =11


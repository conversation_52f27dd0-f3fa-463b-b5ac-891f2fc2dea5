DateDetails As screen:
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)

    BackToCalendar As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(56, 96, 178, 1)
        FontWeight: =FontWeight.Semibold
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(56, 96, 178, 1), -20%)
        OnSelect: |
            =Navigate(
              CalendarView,
              ScreenTransition.Fade
            )
        Size: =14
        Text: ="Back to Calendar"
        Width: =200
        X: =40
        Y: =20
        ZIndex: =1

    LblSelectedDate As label:
        Align: =Align.Center
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Bold
        Height: =60
        Size: =28
        Text: =Text(varSelectedDate, "dddd, mmmm dd, yyyy")
        Width: =800
        X: =200
        Y: =80
        ZIndex: =2

    LblRestrictionsHeader As label:
        Color: =RGBA(211, 66, 9, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Bold
        Size: =20
        Text: ="Calendar Restrictions"
        Width: =400
        X: =40
        Y: =160
        ZIndex: =3

    GalRestrictions As gallery.galleryVertical:
        BorderColor: =RGBA(211, 66, 9, 1)
        BorderThickness: =1
        DelayItemLoading: =true
        Fill: =RGBA(255, 248, 245, 1)
        Height: =180
        Items: |
            =Filter(
                Calendar_Restriction_Indiana,
                DateValue(Date) = DateValue(varSelectedDate)
            )
        Layout: =Layout.Vertical
        LoadingSpinner: =LoadingSpinner.Data
        TemplatePadding: =8
        TemplateSize: =70
        Width: =900
        X: =40
        Y: =210
        ZIndex: =4

        LblRestrictionEvent As label:
            Color: =RGBA(50, 49, 48, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =FontWeight.Semibold
            Height: =30
            OnSelect: =Select(Parent)
            Size: =16
            Text: |
                =If(
                    !IsBlank(ThisItem.Events.Value),
                    ThisItem.Events.Value,
                    ThisItem.Title
                )
            Width: =600
            X: =15
            Y: =8
            ZIndex: =1

        LblRestrictionType As label:
            Color: =RGBA(100, 100, 100, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =25
            OnSelect: =Select(Parent)
            Text: =ThisItem.TypeofEvent.Value
            Width: =600
            X: =15
            Y: =38
            ZIndex: =2

    LblNoRestrictions As label:
        Color: =RGBA(150, 150, 150, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Text: ="No calendar restrictions for this date"
        Visible: =CountRows(GalRestrictions.AllItems) = 0
        Width: =400
        X: =40
        Y: =220
        ZIndex: =5

    LblAbsenteesHeader As label:
        Color: =RGBA(131, 24, 75, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Bold
        Size: =20
        Text: ="Absentees by Role"
        Width: =400
        X: =40
        Y: =420
        ZIndex: =6

    GalAbsentees As gallery.galleryVertical:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderThickness: =1
        DelayItemLoading: =true
        Fill: =RGBA(248, 245, 255, 1)
        Height: =300
        Items: |
            =With(
                {
                    StartDT: varSelectedDate,
                    EndDT: DateAdd(varSelectedDate, 1, TimeUnit.Days) - Time(0, 0, 1)
                },
                Sort(
                    Filter(
                        TimeOffIndiana,
                        DateTimeValue('Leave From') <= EndDT &&
                        DateTimeValue('Leave To') >= StartDT &&
                        If(IsBlank(canceled) || !canceled, true, false)
                    ),
                    Role,
                    SortOrder.Ascending
                )
            )
        Layout: =Layout.Vertical
        LoadingSpinner: =LoadingSpinner.Data
        TemplateSize: =60
        Width: =900
        X: =40
        Y: =470
        ZIndex: =7

        LblEmployeeRole As label:
            Color: =RGBA(131, 24, 75, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =FontWeight.Semibold
            Height: =25
            OnSelect: =Select(Parent)
            Size: =16
            Text: =ThisItem.Role
            Width: =200
            X: =15
            Y: =8
            ZIndex: =1

        LblEmployeeName As label:
            Color: =RGBA(50, 49, 48, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =FontWeight.Semibold
            Height: =25
            OnSelect: =Select(Parent)
            Size: =16
            Text: =ThisItem.Name
            Width: =300
            X: =230
            Y: =8
            ZIndex: =2

        LblLeaveDates As label:
            Color: =RGBA(80, 80, 80, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =25
            OnSelect: =Select(Parent)
            Text: =Text(ThisItem.'Leave From', "mm/dd/yyyy") & " - " & Text(ThisItem.'Leave To', "mm/dd/yyyy")
            Width: =300
            X: =550
            Y: =8
            ZIndex: =3

        LblLeaveType As label:
            Color: =RGBA(100, 100, 100, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =20
            OnSelect: =Select(Parent)
            Size: =12
            Text: =ThisItem.'Leave Type'.Value
            Width: =400
            X: =15
            Y: =35
            ZIndex: =4

    LblNoAbsentees As label:
        Color: =RGBA(150, 150, 150, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Text: ="No absentees for this date"
        Visible: =CountRows(GalAbsentees.AllItems) = 0
        Width: =400
        X: =40
        Y: =480
        ZIndex: =8


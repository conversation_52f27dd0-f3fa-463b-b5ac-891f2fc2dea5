<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/shapes/rectangle" name="rectangle" jsClass="AppMagic.Controls.Shapes.Rectangle" version="2.3.0" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[Rectangle
      Control description here.]]></description>
  <requires>
    <require type="css" src="/ctrllib/shapes/css/shape.css" />
    <require type="javascript" src="/ctrllib/shapes/js/shape.js" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoPointerViewState="true" autoDisabledViewState="true" isVersionFlexible="true" />
  <content><![CDATA[
    <div class="appmagic-svg no-focus-outline" data-bind="
      shortcut: {
        provider: shortcutProvider,
        capture: false
      },
      click: onClickHandler,
      css: {
        readonly: viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit
      },
      attr: {
        title: properties.Tooltip() || null,
        role: (properties.TabIndex() < 0) ? (!!properties.AccessibleLabel() && 'img') : 'button',
        'aria-label': properties.AccessibleLabel() || null,
        'aria-disabled': (properties.TabIndex() >= 0) && (viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit),
        'aria-hidden': properties.TabIndex() < 0 && !properties.AccessibleLabel()
      }">
      <svg version="1.1" baseProfile="tiny" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"
        focusable="false"
        aria-hidden="true"
        touch-action="pan-x pan-y"
        data-bind="
          attr: {
            preserveAspectRatio: properties.PreserveAspectRatio() ? 'xMidYMid meet' : 'none'
          },
          style: {
            width: AppMagic.Controls.converters.pxHorizontalConverter.view(properties.Width()),
            height: AppMagic.Controls.converters.pxVerticalConverter.view(properties.Height())
          }">
        <g>
          <polygon data-bind="attr: {
            fill: fill,
            points: innerPoints
          }" />

          <!-- Border polygon -->
          <polygon data-bind="attr: {
            fill: 'transparent',
            stroke: borderColor,
            'stroke-width': borderThickness,
            'stroke-dasharray': properties.BorderStyle,
            points: outerPoints
          }" />
        </g>
      </svg>
    </div>
    ]]></content>
  <appMagic:includeProperties>
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="ContentLanguage" />
    <appMagic:includeProperty name="Fill" defaultValue="RGBA(0, 176, 240, 1)" isPrimaryOutputProperty="true" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="PressedFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="HoverFill" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="BorderColor" defaultValue="RGBA(0, 0, 255, 1)" />
    <appMagic:includeProperty name="BorderStyle" converter="strokeConverter" />
    <appMagic:includeProperty name="BorderThickness" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="DisplayMode" />
    <appMagic:includeProperty name="TabIndex" defaultValue="-1" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="150" />
    <appMagic:includeProperty name="Height" defaultValue="100" />
    <!-- Behavior Properties -->
    <appMagic:includeProperty name="OnSelect" direction="in" isPrimaryInputProperty="true" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="1" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="1" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
    <appMagic:includeProperty name="PreserveAspectRatio" defaultValue="false" />
  </appMagic:includeProperties>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Popular" priority="60" />
    <appMagic:category name="Shapes" priority="70" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Fill" showInFloatie="true" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" showInCommandBar="true" />
        <appMagic:property name="BorderThickness" showInCommandBar="true" />
        <appMagic:property name="BorderColor" showInCommandBar="true" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledFill" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverFill" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="PressedColor">
        <appMagic:property name="PressedFill" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.1.0">
    <!-- Changed to access Width and Height on measuredProperties -->
    <appMagic:conversionAction type="block" newDocVersion="1.295" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.1.1">
    <!-- Reverted previous change (Width and Height properties now return actual values) -->
  </appMagic:conversion>
  <appMagic:conversion from="2.1.1" to="2.1.2">
    <!-- KO template changes for accessibility fixes -->
  </appMagic:conversion>
  <appMagic:conversion from="2.1.2" to="2.2.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.3.0">
    <!-- Adding showInCommandBar flag -->
  </appMagic:conversion>
</widget>
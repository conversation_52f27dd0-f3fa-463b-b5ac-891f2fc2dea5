DateRestriction As screen:
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)

    BackToAbsences As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(56, 96, 178, 1)
        FontWeight: =FontWeight.Semibold
        Height: =22.54
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Navigate(
              CalendarView,
              ScreenTransition.Fade
            )
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =13.52
        Text: ="Back"
        Width: =180
        Y: =0.00
        ZIndex: =2

    LblAddRestriction As label:
        Align: =Align.Center
        Color: =RGBA(211, 66, 9, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =20
        Text: ="Add New Restriction"
        Width: =400
        X: =400
        Y: =80
        ZIndex: =3

    LblEventsLabel As label:
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =30
        Size: =14
        Text: |-
            ="Events:"
        Width: =100
        X: =400
        Y: =140
        ZIndex: =4

    ComboEvents As combobox:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(131, 24, 75, 1)
        DisplayFields: =["Value"]
        Height: =40
        Items: =Choices(Calendar_Restriction_Indiana.Events)
        SearchFields: =["Value"]
        SearchItems: =Choices(Calendar_Restriction_Indiana.Events,ComboEvents.SearchText)
        SelectMultiple: =false
        Width: =300
        X: =400
        Y: =170
        ZIndex: =5

    LblDateLabel As label:
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =30
        Size: =14
        Text: |-
            ="Date:"
        Width: =100
        X: =400
        Y: =230
        ZIndex: =6

    DatePickerRestriction As datepicker:
        BorderColor: =RGBA(131, 24, 75, 1)
        DisabledBorderColor: =RGBA(0, 0, 0, 0)
        DisabledColor: =RGBA(0, 0, 0, 0)
        DisabledFill: =RGBA(0, 0, 0, 0)
        Height: =40
        Width: =300
        X: =400
        Y: =260
        ZIndex: =7

    LblTypeLabel As label:
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =30
        Size: =14
        Text: |-
            ="Type of Event:"
        Width: =150
        X: =400
        Y: =320
        ZIndex: =8

    ComboTypeOfEvent As combobox:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(131, 24, 75, 1)
        DisplayFields: =["Value"]
        Height: =40
        Items: =Choices(Calendar_Restriction_Indiana.'Type of Event')
        SearchFields: =["Value"]
        SearchItems: =Choices(Calendar_Restriction_Indiana.'Type of Event',ComboTypeOfEvent.SearchText)
        SelectMultiple: =false
        Width: =300
        X: =400
        Y: =350
        ZIndex: =9

    BtnSaveRestriction As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(211, 66, 9, 1)
        FontWeight: =FontWeight.Semibold
        Height: =45
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Patch(
                Calendar_Restriction_Indiana,
                Defaults(Calendar_Restriction_Indiana),
                {
                    Events: ComboEvents.Selected,
                    Date: DatePickerRestriction.SelectedDate,
                    TypeofEvent: ComboTypeOfEvent.Selected
                }
            );
            Reset(ComboEvents);
            Reset(DatePickerRestriction);
            Reset(ComboTypeOfEvent);
            Notify("Restriction added successfully!", NotificationType.Success)
        Size: =16
        Text: ="Save Restriction"
        Width: =200
        X: =400
        Y: =420
        ZIndex: =10

    BtnCancelRestriction As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(150, 150, 150, 1)
        FontWeight: =FontWeight.Semibold
        Height: =45
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(150, 150, 150, 1), -20%)
        OnSelect: |
            =Reset(ComboEvents);
            Reset(DatePickerRestriction);
            Reset(ComboTypeOfEvent)
        Size: =16
        Text: ="Clear Form"
        Width: =150
        X: =620
        Y: =420
        ZIndex: =11


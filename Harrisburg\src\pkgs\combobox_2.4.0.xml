<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/combobox" name="combobox" jsClass="AppMagic.Controls.ComboBox.ComboBoxControl" version="2.4.0" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[COMBOBOX
      Control description here.]]></description>
  <requires>
    <require type="javascript" src="/PCF/prop-types.js" />
    <require type="javascript" src="/js/FabricForPublishedApps.js" />
    <require type="javascript" src="/ctrllib/flyout/js/flyoutReact.js" />
    <require type="javascript" src="/ctrllib/combobox/js/comboboxReact.js" />
    <require type="other" src="/ctrllib/combobox/data/ComboBoxSample.xlsx" authoringOnly="true" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoBorders="true" autoFocusedBorders="true" screenActiveAware="true" autoDisabledViewState="true" autoPointerViewState="true" isVersionFlexible="true" />
  <appMagic:accessibilityChecks controlIsInteractive="true" />
  <content><![CDATA[
    <div class="appmagic-combobox">
    </div>
    ]]></content>
  <properties>
    <!-- hidden properties -->
    <!-- TASK: 85476: Do Behavior properties make sense as input only? -->
    <property name="OnNavigate" localizedName="##combobox_OnNavigate##" hidden="true" datatype="Boolean" defaultValue="false" direction="in">
      <title>ComboBox navigate behavior</title>
      <appMagic:category>behavior</appMagic:category>
      <appMagic:displayName>##combobox_OnNavigate_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_OnNavigate_Tooltip##</appMagic:tooltip>
    </property>
    <property name="NavigateFields" localizedName="##combobox_NavigateFields##" datatype="Array" hidden="true" direction="in" isExpr="true" defaultValue="[]" editable="true">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_NavigateFields_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_NavigateFields_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Template" localizedName="##comboBox_Template##" datatype="ListItemTemplate" hidden="true" defaultValue="%ListItemTemplate.RESERVED%.Single" isExpr="true">
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##comboBox_Template_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##comboBox_Template_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SearchItems" localizedName="##combobox_SearchItems##" datatype="Array" hidden="true" direction="in" isExpr="true" defaultValue="Search(ComboBoxSample, Self.SearchText, Value1)">
      <title>Filtered items from search</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_SearchItems_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SearchItems_Tooltip##</appMagic:tooltip>
    </property>
    <property name="UsePhoneLayout" localizedName="##combobox_UsePhoneLayout##" datatype="Boolean" defaultValue="false" phoneDefaultValue="true" hidden="true">
      <appMagic:category>design</appMagic:category>
    </property>
    <property name="MoreItemsButtonColor" localizedName="##combobox_MoreItemsButtonColor##" datatype="Color" defaultValue="Self.ChevronBackground" isExpr="true" converter="argbConverter" hidden="true">
      <title>"More" button color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##combobox_MoreItemsButtonColor_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_MoreItemsButtonColor_Tooltip##</appMagic:tooltip>
    </property>
    <!-- /hidden properties -->
    <property name="DisplayFields" localizedName="##combobox_DisplayFields##" datatype="Array" direction="in" isExpr="true" defaultValue="[&quot;Value1&quot;]" editable="true">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_DisplayFields_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_DisplayFields_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SearchFields" localizedName="##combobox_SearchFields##" datatype="Array" direction="in" isExpr="true" defaultValue="[&quot;Value1&quot;]" editable="true">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_SearchFields_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SearchFields_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Items" localizedName="##combobox_Items##" datatype="Array" direction="in" editable="true" isPrimaryInputProperty="true">
      <title>dropdown items</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_Items_DisplayName##</appMagic:displayName>
      <appMagic:sampleDataSource name="ComboBoxSample" location="data/ComboBoxSample.xlsx" />
      <appMagic:tooltip>##combobox_Items_Tooltip##</appMagic:tooltip>
    </property>
    <property name="InputTextPlaceholder" localizedName="##combobox_InputTextPlaceholder##" datatype="String" direction="in" defaultValue="##combobox_FindItemsText##" editable="true">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_InputTextPlaceholder_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_InputTextPlaceholder_Tooltip##</appMagic:tooltip>
    </property>
    <property name="NoSelectionText" localizedName="##combobox_NoSelectionText##" datatype="String" direction="in" defaultValue="" editable="true">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_NoSelectionText_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_NoSelectionText_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SelectMultiple" localizedName="##combobox_SelectMultiple##" datatype="Boolean" defaultValue="true" direction="in" editable="true">
      <title>SelectMultiple</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_SelectMultiple_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SelectMultiple_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Default" localizedName="##combobox_Default##" datatype="object" defaultValue="" editable="true" direction="in">
      <title>Default selected item</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_Default_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_Default_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Selected" localizedName="##combobox_Selected##" datatype="object" direction="out" supportsAutomation="true">
      <title>Selected item that is going to be passed through</title>
      <appMagic:passThroughReference>Items</appMagic:passThroughReference>
      <appMagic:category>data</appMagic:category>
    </property>
    <property name="NavigateItem" localizedName="##combobox_NavigateItem##" datatype="object" direction="out">
      <title>Data item for use when OnNavigate is called.</title>
      <appMagic:passThroughReference>Items</appMagic:passThroughReference>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_NavigateItem_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_NavigateItem_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SelectedItems" localizedName="##combobox_SelectedItems##" datatype="Array" direction="out" isPrimaryOutputProperty="true" supportsAutomation="true">
      <title>Selected items in the control.</title>
      <appMagic:passThroughReference filtered="true">Items</appMagic:passThroughReference>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_SelectedItems_DisplayName##</appMagic:displayName>
    </property>
    <property name="SearchText" localizedName="##combobox_SearchText##" datatype="String" direction="out">
      <title>Text used to search the list of items.</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_SearchText_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SearchText_Tooltip##</appMagic:tooltip>
    </property>
    <property name="DefaultSelectedItems" localizedName="##combobox_DefaultSelectedItems##" defaultValue="First(ComboBoxSample)" datatype="Array" editable="true" direction="in">
      <title>Default selected items</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_DefaultSelectedItems_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_DefaultSelectedItems_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Reset" localizedName="##commonProperties_Reset##" datatype="Boolean" defaultValue="false" direction="in">
      <title>Reset</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip>
    </property>
    <property name="IsSearchable" localizedName="##combobox_IsSearchable##" datatype="Boolean" defaultValue="true" direction="in">
      <title>IsSearchable</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_IsSearchable_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_IsSearchable_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SelectionColor" localizedName="##combobox_SelectionColor##" datatype="Color" defaultValue="RGBA(255, 255, 255, 1)" isExpr="true" converter="argbConverter">
      <title>Selected Text color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##combobox_SelectionColor_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SelectionColor_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ChevronWidth" localizedName="##ComboBox_Chevron_Width##" datatype="Number" defaultValue="34" phoneDefaultValue="50" webDefaultValue="32" converter="pxHorizontalConverter" hidden="true" styleable="true">
      <title>ComboBox Chevron Width</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##ComboBox_Chevron_Width_DisplayName##</appMagic:displayName>
    </property>
    <property name="FlyoutMaximumHeight" localizedName="##ComboBox_Flyout_MaximumHeight##" datatype="Number" defaultValue="400" converter="pxVerticalConverter" phoneDefaultValue="700" hidden="true" styleable="true">
      <title>ComboBox Flyout Maximum Height</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##ComboBox_Flyout_MaximumHeight_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronFill" localizedName="##ComboBox_Chevron_Fill##" datatype="Color" isExpr="true" defaultValue="RGBA(255, 255, 255, 1)" converter="argbConverter">
      <title>ComboBox Chevron Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##ComboBox_Chevron_Fill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##ComboBox_Chevron_Fill_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ChevronHoverFill" localizedName="##ComboBox_Chevron_HoverFill##" datatype="Color" isExpr="true" defaultValue="Self.ChevronFill" converter="argbConverter" styleable="true">
      <title>ComboBox Chevron Hover Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##ComboBox_Chevron_HoverFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronDisabledFill" localizedName="##ComboBox_Chevron_DisabledFill##" datatype="Color" isExpr="true" defaultValue="RGBA(235, 233, 229, 1)" converter="argbConverter" styleable="true">
      <title>ComboBox Chevron Disabled Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##ComboBox_Chevron_DisabledFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronBackground" localizedName="##ComboBox_Chevron_Background##" datatype="Color" isExpr="true" defaultValue="RGBA(35, 31, 32, 1)" converter="argbConverter">
      <title>ComboBox Chevron Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##ComboBox_Chevron_Background_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##ComboBox_Chevron_Background_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ChevronHoverBackground" localizedName="##ComboBox_Chevron_HoverBackground##" datatype="Color" isExpr="true" defaultValue="ColorFade(Self.ChevronBackground, 20%)" converter="argbConverter" styleable="true">
      <title>ComboBox Chevron Hover Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##ComboBox_Chevron_HoverBackground_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronDisabledBackground" localizedName="##ComboBox_Chevron_DisabledBackground##" datatype="Color" isExpr="true" defaultValue="RGBA(215, 210, 204, 1)" converter="argbConverter" styleable="true">
      <title>ComboBox chevron disabled fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##ComboBox_Chevron_DisabledBackground_DisplayName##</appMagic:displayName>
    </property>
    <property name="SelectionFill" localizedName="##combobox_SelectionFill##" datatype="Color" defaultValue="RGBA(0, 176, 240, 1)" isExpr="true" converter="argbConverter">
      <title>Selected fill color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##combobox_SelectionFill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SelectionFill_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SelectionTagFill" localizedName="##combobox_SelectionTagFill##" datatype="Color" defaultValue="Self.HoverFill" isExpr="true" converter="argbConverter">
      <title>Selection tag fill color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##combobox_SelectionTagFill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SelectionTagFill_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SelectionTagColor" localizedName="##combobox_SelectionTagColor##" datatype="Color" defaultValue="Self.HoverColor" isExpr="true" converter="argbConverter">
      <title>Selection tag color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##combobox_SelectionTagColor_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_SelectionTagColor_Tooltip##</appMagic:tooltip>
    </property>
    <property name="MultiValueDelimiter" localizedName="##combobox_MultiValueDelimiter##" datatype="String" direction="in" defaultValue=", " editable="true" visible="false">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##combobox_MultiValueDelimiter_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##combobox_MultiValueDelimiter_Tooltip##</appMagic:tooltip>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Data -->
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="Color" defaultValue="RGBA(0, 0, 0, 1)" isExpr="true" />
    <appMagic:includeProperty name="HoverColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="PressedColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="BorderColor" defaultValue="RGBA(69, 69, 69, 1)" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="ColorFade(Self.BorderColor, 15%)" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="Self.HoverBorderColor" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" defaultValue="2" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="4" />
    <appMagic:includeProperty name="Fill" defaultValue="RGBA(255, 255, 255, 1)" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="RGBA(242, 242, 242, 1)" />
    <appMagic:includeProperty name="PressedFill" defaultValue="ColorFade(Self.SelectionFill, -30%)" />
    <appMagic:includeProperty name="HoverFill" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="Font" />
    <appMagic:includeProperty name="Size" phoneDefaultValue="30" />
    <appMagic:includeProperty name="FontWeight" />
    <appMagic:includeProperty name="Italic" />
    <appMagic:includeProperty name="Underline" />
    <appMagic:includeProperty name="Strikethrough" />
    <appMagic:includeProperty name="PaddingTop" defaultValue="5" phoneDefaultValue="10" />
    <appMagic:includeProperty name="PaddingRight" defaultValue="5" phoneDefaultValue="10" />
    <appMagic:includeProperty name="PaddingBottom" defaultValue="5" phoneDefaultValue="10" />
    <appMagic:includeProperty name="PaddingLeft" defaultValue="5" phoneDefaultValue="10" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="328" phoneDefaultValue="575" webDefaultValue="180" />
    <appMagic:includeProperty name="Height" defaultValue="40" phoneDefaultValue="70" webDefaultValue="32" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="TabIndex" />
    <appMagic:includeProperty name="DisplayMode" />
    <!-- Behavior -->
    <appMagic:includeProperty name="OnSelect" direction="in" />
    <appMagic:includeProperty name="OnChange" direction="in" isPrimaryBehaviorProperty="true" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="80" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="35" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
  </appMagic:includeProperties>
  <!--Property Dependencies -->
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="Items" output="Selected" />
    <appMagic:propertyDependency input="SearchItems" output="Selected" />
    <appMagic:propertyDependency input="Default" output="Selected" />
    <appMagic:propertyDependency input="DefaultSelectedItems" output="Selected" />
    <appMagic:propertyDependency input="Reset" output="Selected" />
    <appMagic:propertyDependency input="Items" output="SelectedItems" />
    <appMagic:propertyDependency input="SearchItems" output="SelectedItems" />
    <appMagic:propertyDependency input="Default" output="SelectedItems" />
    <appMagic:propertyDependency input="DefaultSelectedItems" output="SelectedItems" />
    <appMagic:propertyDependency input="Reset" output="SelectedItems" />
  </appMagic:propertyDependencies>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Input" priority="50" />
    <appMagic:category name="ClassicControls" priority="50" />
  </appMagic:insertMetadata>
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Items" labelOverride="##ControlSidebar_PropertiesPanel_Datasource_Displayname##" serverProvidesValue="true" computedValueType="NativeCdsDataSourceName" />
      <appMagic:dataSourceSelectionCallout dataSourcePropertyName="Items" />
      <appMagic:configureCdsViews propertyToReplace="Items" />
      <appMagic:configureProperty propertyInvariantName="DependentItems" ruleToSet="Items" serverProvidesValue="true" computedValueType="DataSourceName" />
      <appMagic:wizardPropertyGroup>
        <appMagic:wizardStep label="##ControlSidebar_PropertiesPanel_Fields_DisplayName##" panelTitle="##ControlSidebar_PropertiesPanel_Data_DisplayName##" linkedLocation="DataPanelComboBoxCustomization" propertyInvariantName="Fields" computedValueType="FieldSummaryEdit" serverProvidesValue="true" />
      </appMagic:wizardPropertyGroup>
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Padding">
        <appMagic:property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <appMagic:property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <appMagic:property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <appMagic:property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Color" showInFloatie="true" />
        <appMagic:property name="Fill" showInFloatie="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="SelectionColor">
        <appMagic:property name="SelectionColor" />
        <appMagic:property name="SelectionFill" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="ChevronColor">
        <appMagic:property name="ChevronFill" />
        <appMagic:property name="ChevronBackground" />
      </appMagic:propertyGroup>
      <appMagic:property name="Font" displayType="FontEnum" showInFloatie="true" />
      <appMagic:property name="Size" labelOverride="##FontSize_Property##" showInFloatie="true" />
      <appMagic:property name="FontWeight" displayType="EnumIcon" itemsOrder="Bold;Semibold;Normal;Lighter" />
      <appMagic:propertyGroup name="Style">
        <appMagic:property name="Italic" displayType="ToggleButton" />
        <appMagic:property name="Underline" displayType="ToggleButton" />
        <appMagic:property name="Strikethrough" displayType="ToggleButton" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="SelectMultiple" />
      <appMagic:property name="IsSearchable" />
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledColor" />
        <appMagic:property name="DisabledFill" />
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="ChevronDisabledFill" />
      <appMagic:property name="ChevronDisabledBackground" />
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverColor" />
        <appMagic:property name="HoverFill" />
        <appMagic:property name="HoverBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="ChevronHoverFill" />
      <appMagic:property name="ChevronHoverBackground" />
      <appMagic:propertyGroup name="PressedColor">
        <appMagic:property name="PressedColor" />
        <appMagic:property name="PressedFill" />
        <appMagic:property name="PressedBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:configureFields propertyInvariantName="SearchFields" dataSourcePropertyName="Items" supportsCollection="true" isEditable="true" displayFormat="PropertiesDisplayOrder" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.1.0">
    <appMagic:conversionAction type="add" name="MoreItemsButtonColor" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <!-- Added flyout control library -->
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.3.0">
    <appMagic:conversionAction type="add" name="NoSelectionText" />
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.3.1">
    <!--
      Changed default value of MultiValueDelimiter from ",&ensp;" to ", "
      because Chromium browsers could not tokenize the string properly for Narrator
    -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.1" to="2.4.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
</widget>
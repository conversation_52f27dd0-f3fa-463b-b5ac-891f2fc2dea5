{"ControlStates": {"Body1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Title", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Align", "IsLockable": false, "NameMapSourceSchema": "?"}, "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "Size"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryBody.ID%", "Name": "Body1", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "contentLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "DateRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "ImagePosition", "Height", "<PERSON><PERSON><PERSON>", "Size", "Orientation", "LoadingSpinner", "LoadingSpinnerColor"], "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DateRestriction", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ImagePosition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Orientation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}], "StyleName": "defaultScreenStyle", "Type": "ControlInfo"}, "Gallery1": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "WrapCount", "TemplateSize", "TemplatePadding", "Layout", "Transition", "DelayItemLoading", "LoadingSpinner", "LoadingSpinnerColor", "DisplayMode", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "ZIndex"], "GalleryTemplateChildName": "galleryTemplate3", "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Gallery1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "WrapCount", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplatePadding", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Layout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Transition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DelayItemLoading", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "defaultGalleryStyle", "Type": "ControlInfo"}, "galleryTemplate3": {"AllowAccessToGlobals": true, "ControlPropertyState": ["TemplateFill"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "galleryTemplate3", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "TemplateFill", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "Rectangle4": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "FocusedBorderColor", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "OnSelect", "ZIndex", "Visible", "BorderThickness", "TabIndex"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryRectangle1.ID%", "Name": "Rectangle4", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "primary2RectangleStyle", "Type": "ControlInfo"}, "Separator10": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "DisabledFill", "PressedFill", "HoverFill", "BorderColor", "BorderStyle", "FocusedBorderColor", "DisplayMode", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "OnSelect", "ZIndex", "BorderThickness", "TabIndex"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGallerySeparator1.ID%", "Name": "Separator10", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "dividerStyle2020", "Type": "ControlInfo"}, "Subtitle3": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "Overflow", "Text", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "Size"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGallerySubtitle.ID%", "Name": "Subtitle3", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "subtitleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "Title4": {"AllowAccessToGlobals": true, "ControlPropertyState": [{"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "Live", "Overflow", "Role", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "FocusedBorderColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "DisplayMode", "OnSelect", "ZIndex", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "Size"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "%TemplateBrowseGalleryTitle.ID%", "Name": "Title4", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "titleLabelStyle_galleryLayouts_ver5", "Type": "ControlInfo"}, "Title4_1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.Events.Value", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Size", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Title4_1", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}}, "TopParentName": "DateRestriction"}
AbsencesByDate As screen:
    Fill: =RGBA(242,242,242,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)
    OnVisible: |-
        =// Initialize the sort variables when the screen loads
        UpdateContext({
            varSortColumn: "Requested Time", // The name of the column to sort by
            varSortDirection: SortOrder.Ascending // The initial sort direction
        })

    "Gal_Absences As gallery.'BrowseLayout_Vertical_TwoTextVariant_ver5.0'":
        BorderColor: =RGBA(131, 24, 75, 1)
        DelayItemLoading: =true
        Height: =499
        Items: |-
            =// --- Outer With: Defines the date/time variables. ---
            With(
              {
                StartDT:
                  DateAdd(
                    DatePicker_Start.SelectedDate,
                    Value(Dropdown_HourStart.Selected.Value),
                    "Hours"
                  )
                  + Time(
                      0,
                      Value(Dropdown_MinStart.Selected.Value),
                      0
                    ),
                EndDT:
                  DateAdd(
                    DatePicker_End.SelectedDate,
                    Value(Dropdown_HourEnd.Selected.Value),
                    "Hours"
                  )
                  + Time(
                      0,
                      Value(Dropdown_MinEnd.Selected.Value),
                      0
                    )
              },
              
              // --- Inner With: Creates FilteredRecords. ---
              With(
                {
                  FilteredRecords:
                    Filter(
                      TimeOffIndiana,
                      DateTimeValue('Leave From') <= EndDT &&
                      DateTimeValue('Leave To') >= StartDT &&
                      If(
                        tglShowCanceled.Value = true,
                        canceled = true,
                        canceled = false || IsBlank(canceled)
                      )
                    )
                },
                
                // --- NEW DYNAMIC SORTING LOGIC ---
                // Checks the varSortColumn variable to decide how to sort
                If(
                  varSortColumn = "Role",
                  
                  // -- Sort by Classification --
                  Sort(
                    FilteredRecords,
                    Role, // Use your actual column name
                    varSortDirection  // Use the direction variable
                  ),
                  
                  // -- Default/Sort by Request Time --
                  Sort(
                    FilteredRecords,
                    DateTimeValue('Requested Time'),
                    varSortDirection // Use the direction variable
                  )
                )
              )
            )
        Layout: =Layout.Vertical
        LoadingSpinner: =LoadingSpinner.Data
        TemplatePadding: =0.00
        TemplateSize: =50.70
        Width: =816
        Y: =141
        ZIndex: =1

        Leave_to As label:
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =27
            OnSelect: =Select(Parent)
            PaddingBottom: =0
            PaddingLeft: =0
            PaddingRight: =0
            PaddingTop: =0
            Size: =11.27
            Text: =ThisItem.'Leave To'
            Width: =162
            X: =453
            Y: =19
            ZIndex: =2

        Name As label:
            Color: =RGBA(50, 49, 48, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =21.30
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =11.83
            Text: =ThisItem.Name
            VerticalAlign: =VerticalAlign.Top
            Width: =Parent.TemplateWidth - 86
            X: =16
            Y: =4.56
            ZIndex: =3

        Leave_from As label:
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =22
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =11.27
            Text: =ThisItem.'Leave From'
            VerticalAlign: =VerticalAlign.Top
            Width: =153
            X: =297
            Y: =24
            ZIndex: =4

        RequestedTime As label:
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =26
            PaddingBottom: =0
            PaddingLeft: =0
            PaddingRight: =0
            PaddingTop: =0
            Size: =11.27
            Text: =ThisItem.'Requested Time'
            Width: =152
            X: =145
            Y: =19
            ZIndex: =5

        Separator15 As rectangle:
            BorderColor: =RGBA(131, 24, 75, 1)
            Fill: =RGBA(255, 255, 255, 1)
            Height: =4.51
            OnSelect: =Select(Parent)
            Width: =Parent.TemplateWidth
            Y: =46.20
            ZIndex: =6

        Rectangle14 As rectangle:
            BorderColor: =RGBA(131, 24, 75, 1)
            Fill: =RGBA(131, 24, 75, 1)
            Height: =46.20
            OnSelect: =Select(Parent)
            Visible: =ThisItem.IsSelected
            Width: =4
            Y: =0.00
            ZIndex: =7

        LeaveType As label:
            Color: =RGBA(50, 49, 48, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =15
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =11.83
            Text: =ThisItem.'Leave Type'.Value
            VerticalAlign: =VerticalAlign.Top
            Width: =68
            X: =615
            Y: =24
            ZIndex: =8

        Classification As label:
            Color: =RGBA(50, 49, 48, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =15
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =11.83
            Text: =ThisItem.Role
            VerticalAlign: =VerticalAlign.Top
            Width: =90
            X: =697
            Y: =24
            ZIndex: =9

    Back As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(56, 96, 178, 1)
        FontWeight: =FontWeight.Semibold
        Height: =22.54
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Navigate(
              EnterData, 
              ScreenTransition.Fade
            )
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =13.52
        Text: ="Back"
        Width: =180
        Y: =0.00
        ZIndex: =2

    DatePicker_Start As datepicker:
        BorderColor: =RGBA(131, 24, 75, 1)
        CalendarHeaderFill: =RGBA(211, 66, 9, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(242, 242, 242, 0.01)
        Height: =39.44
        HoverDateFill: =RGBA(255, 211, 205, 1)
        IconBackground: =RGBA(211, 66, 9, 1)
        PaddingBottom: =2.82
        PaddingLeft: =6.76
        SelectedDateFill: =RGBA(211, 66, 9, 1)
        Size: =11.83
        Width: =306
        X: =90
        Y: =22
        ZIndex: =4

    DatePicker_End As datepicker:
        BorderColor: =RGBA(131, 24, 75, 1)
        CalendarHeaderFill: =RGBA(211, 66, 9, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Height: =39.44
        HoverDateFill: =RGBA(255, 211, 205, 1)
        IconBackground: =RGBA(211, 66, 9, 1)
        SelectedDateFill: =RGBA(211, 66, 9, 1)
        Size: =11.83
        Width: =306
        X: =90
        Y: =77
        ZIndex: =5

    Dropdown_MinEnd As dropdown:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(211, 66, 9, 1)
        ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
        ChevronDisabledFill: =RGBA(244, 244, 244, 1)
        ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        ChevronHoverFill: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Height: =39.44
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Items: =["00","15","30","45"]
        PressedColor: =RGBA(255, 255, 255, 1)
        PressedFill: =RGBA(131, 24, 75, 1)
        SelectionFill: =RGBA(211, 66, 9, 1)
        Size: =13.52
        Width: =97
        X: =548
        Y: =77
        ZIndex: =6

    Dropdown_HourEnd As dropdown:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(211, 66, 9, 1)
        ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
        ChevronDisabledFill: =RGBA(244, 244, 244, 1)
        ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        ChevronHoverFill: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Height: =39.44
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Items: |
            =["00","01","02","03","04","05","06","07","08","09","10","11",
             "12","13","14","15","16","17","18","19","20","21","22","23"]
        PressedColor: =RGBA(255, 255, 255, 1)
        PressedFill: =RGBA(131, 24, 75, 1)
        SelectionFill: =RGBA(211, 66, 9, 1)
        Size: =11.83
        Width: =103
        X: =423
        Y: =76
        ZIndex: =7

    Dropdown_MinStart As dropdown:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(211, 66, 9, 1)
        ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
        ChevronDisabledFill: =RGBA(244, 244, 244, 1)
        ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        ChevronHoverFill: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Height: =39.44
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Items: =["00","15","30","45"]
        PressedColor: =RGBA(255, 255, 255, 1)
        PressedFill: =RGBA(131, 24, 75, 1)
        SelectionFill: =RGBA(211, 66, 9, 1)
        Size: =11.83
        Width: =97
        X: =548
        Y: =22
        ZIndex: =8

    Dropdown_HourStart As dropdown:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(211, 66, 9, 1)
        ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
        ChevronDisabledFill: =RGBA(244, 244, 244, 1)
        ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        ChevronHoverFill: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Height: =39
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Items: |
            =["00","01","02","03","04","05","06","07","08","09","10","11",
             "12","13","14","15","16","17","18","19","20","21","22","23"]
        PressedColor: =RGBA(255, 255, 255, 1)
        PressedFill: =RGBA(131, 24, 75, 1)
        SelectionFill: =RGBA(211, 66, 9, 1)
        Size: =13.52
        Width: =103
        X: =423
        Y: =22
        ZIndex: =9

    TextRequestTime As label:
        Align: =Align.Center
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =34
        PaddingBottom: =0
        PaddingLeft: =0
        PaddingRight: =0
        PaddingTop: =0
        Size: =12
        Text: ="Request Time"
        Width: =122
        X: =152
        Y: =116
        ZIndex: =10

    TextStart As label:
        Align: =Align.Center
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =34
        PaddingBottom: =0
        PaddingLeft: =0
        PaddingRight: =0
        PaddingTop: =0
        Size: =12
        Text: ="Start"
        Width: =122
        X: =301
        Y: =115
        ZIndex: =11

    TextEnd As label:
        Align: =Align.Center
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =34
        PaddingBottom: =0
        PaddingLeft: =0
        PaddingRight: =0
        PaddingTop: =0
        Size: =12
        Text: ="End"
        Width: =122
        X: =455
        Y: =115
        ZIndex: =12

    TextFrom As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =32
        Size: =11.83
        Text: |-
            ="From:"
        Width: =120
        Y: =25
        ZIndex: =13

    TextTo As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =32
        Size: =11.83
        Text: |-
            ="To:"
        Width: =120
        Y: =80
        ZIndex: =14

    TextLeaveType As label:
        Align: =Align.Center
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =34
        PaddingBottom: =0
        PaddingLeft: =0
        PaddingRight: =0
        PaddingTop: =0
        Size: =12
        Text: ="Leave Type"
        Width: =122
        X: =560
        Y: =115
        ZIndex: =16

    TextClassification As label:
        Align: =Align.Center
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Semibold
        Height: =34
        PaddingBottom: =0
        PaddingLeft: =0
        PaddingRight: =0
        PaddingTop: =0
        Size: =12
        Text: ="Classification"
        Width: =122
        X: =679
        Y: =115
        ZIndex: =17

    tglShowCanceled As toggleSwitch:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        FalseFill: =RGBA(128, 130, 133, 1)
        FalseText: ="No"
        HandleFill: =RGBA(255, 255, 255, 1)
        Height: =27.61
        Size: =11.83
        TrueFill: =RGBA(211, 66, 9, 1)
        TrueText: ="Yes"
        Width: =100
        X: =679
        Y: =69
        ZIndex: =19

    TexttglShowCanceled As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        FontWeight: =FontWeight.Bold
        Height: =43
        Size: =9
        Text: ="Show Canceled Requests"
        Width: =183
        X: =659
        Y: =31
        ZIndex: =20

    icoSortRequestTime As icon.ArrowDown:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderThickness: =2
        Color: =RGBA(99, 139, 44, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        FocusedBorderThickness: =4
        Height: =21
        OnSelect: |-
            =If(
                varSortColumn = "Requested Time",
                // If this column is already active, just toggle the direction
                UpdateContext({ varSortDirection: If(varSortDirection = SortOrder.Ascending, SortOrder.Descending, SortOrder.Ascending) }),
                // Otherwise, make this the active column and set direction to Ascending
                UpdateContext({ varSortColumn: "Requested Time", varSortDirection: SortOrder.Ascending })
            )
        Rotation: |-
            =If(
                varSortColumn = "Requested Time" && varSortDirection = SortOrder.Ascending,
                180, // Flip it upside down
                0    // Keep it upright
            )
        Width: =32
        X: =120
        Y: =123
        ZIndex: =24

    icoSortClassification As icon.ArrowUp:
        BorderColor: =RGBA(131, 24, 75, 1)
        BorderThickness: =2
        Color: =RGBA(99, 139, 44, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        FocusedBorderThickness: =4
        Height: =21
        Icon: =Icon.ArrowUp
        OnSelect: |-
            =If(
                varSortColumn = "Role",
                // If this column is already active, just toggle the direction
                UpdateContext({ varSortDirection: If(varSortDirection = SortOrder.Ascending, SortOrder.Descending, SortOrder.Ascending) }),
                // Otherwise, make this the active column and set direction to Ascending
                UpdateContext({ varSortColumn: "Role", varSortDirection: SortOrder.Ascending })
            )
        Rotation: |-
            =If(
                varSortColumn = "Role" && varSortDirection = SortOrder.Ascending,
                180, // Flip it upside down
                0    // Keep it upright
            )
        Width: =32
        X: =795
        Y: =118
        ZIndex: =25

    RestrictionAdd As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(211, 66, 9, 1)
        FontWeight: =FontWeight.Semibold
        Height: =45
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Navigate(
              DateRestriction,
              ScreenTransition.Fade
            )
        Size: =16
        Text: ="Add Restriction"
        Width: =200
        X: =920
        Y: =16
        ZIndex: =26


<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/button" name="button" jsClass="AppMagic.Controls.Button" version="2.2.0" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[BUTTON
      Control description here.]]></description>
  <requires>
    <require type="css" src="/ctrllib/common/css/button.css" />
    <require type="javascript" src="/ctrllib/common/js/button.js" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoBorders="true" autoFocusedBorders="true" autoFill="true" autoPointerViewState="true" autoDisabledViewState="true" autoBorderRadius="true" isVersionFlexible="true" supportsSetFocus="true" />
  <appMagic:accessibilityChecks controlIsInteractive="true" />
  <content><![CDATA[
<div class="appmagic-button-wrapper">
  <div
    class="a11y appmagic-button-busy"
    tabindex="-1"
    data-bind="
      style: {
        display: viewState.isAutoDisabled() ? null : 'none'
      },
      text: AppMagic.Strings.ButtonBusyMessage
    "
  ></div>

  <button
    class="appmagic-button-container no-focus-outline"
    data-control-part="button"
    data-bind="
      event: {
        click: handleClick,
        pointerdown: handleMouseDown,
        pointerup: handleMouseUp,
        pointerout: handleMouseOut
      },
      attr: {
        title: properties.Tooltip,
        disabled: viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit
      }
    "
  >
    <div
      class="appmagic-button"
      touch-action="pan-x pan-y"
      data-bind="
        style: {
            fontFamily: properties.Font,
            fontSize: properties.Size,
            color: autoProperties.Color,
            fontWeight: properties.FontWeight,
            fontStyle: properties.Italic,
            textAlign: properties.Align,
            paddingTop: properties.PaddingTop,
            paddingRight: properties.PaddingRight,
            paddingBottom: properties.PaddingBottom,
            paddingLeft: properties.PaddingLeft,
        },
        css: {
            top: properties.VerticalAlign() === 'top',
            middle: properties.VerticalAlign() === 'middle',
            bottom: properties.VerticalAlign() === 'bottom',
            left: properties.Align() === 'left',
            right: properties.Align() === 'right',
            center: properties.Align() === 'center',
            justify: properties.Align() === 'justify',
            disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled,
            underline: properties.Underline,
            strikethrough: properties.Strikethrough,
            readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View
        }
      "
    >
      <div
        class="appmagic-button-label no-focus-outline"
        data-control-part="text"
        spellcheck="false"
        data-bind="inlineEditText: properties.Text"
      >
      </div>
    </div>
  </button>
<div>
]]></content>
  <properties>
    <property name="Pressed" localizedName="##button_Pressed##" datatype="Boolean" direction="out" defaultValue="false" isPrimaryOutputProperty="true">
      <title>The pressed state of the button.</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:helperUI>boolean</appMagic:helperUI>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Behavior -->
    <appMagic:includeProperty name="OnSelect" direction="in" isPrimaryInputProperty="true" />
    <appMagic:includeProperty name="AutoDisableOnSelect" />
    <!-- Data -->
    <appMagic:includeProperty name="Text" defaultValue="##Button_DefaultValue_Text##" isExpr="true" />
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="BorderColor" defaultValue="ColorFade(Self.Fill, -15%)" />
    <!-- TASK: 4548082: Add the Color and Fill in default theme json -->
    <appMagic:includeProperty name="RadiusTopLeft" defaultValue="10" />
    <appMagic:includeProperty name="RadiusTopRight" defaultValue="10" />
    <appMagic:includeProperty name="RadiusBottomLeft" defaultValue="10" />
    <appMagic:includeProperty name="RadiusBottomRight" defaultValue="10" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="ColorFade(Self.BorderColor, 70%)" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="ColorFade(Self.BorderColor, 20%)" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" defaultValue="2" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="4" />
    <appMagic:includeProperty name="Color" defaultValue="RGBA(255, 255, 255, 1)" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="ColorFade(Self.Fill, 90%)" />
    <appMagic:includeProperty name="PressedColor" defaultValue="Self.Fill" />
    <appMagic:includeProperty name="HoverColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="DisplayMode" />
    <appMagic:includeProperty name="Fill" defaultValue="RGBA(35, 31, 32, 1)" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="ColorFade(Self.Fill, 70%)" />
    <appMagic:includeProperty name="PressedFill" defaultValue="Self.Color" />
    <appMagic:includeProperty name="HoverFill" defaultValue="ColorFade(Self.Fill, 20%)" />
    <appMagic:includeProperty name="Font" />
    <appMagic:includeProperty name="Size" defaultValue="13" phoneDefaultValue="23" />
    <appMagic:includeProperty name="FontWeight" defaultValue="%FontWeight.RESERVED%.Normal" />
    <appMagic:includeProperty name="Italic" />
    <appMagic:includeProperty name="Underline" />
    <appMagic:includeProperty name="Strikethrough" />
    <appMagic:includeProperty name="Align" defaultValue="%Align.RESERVED%.Center" />
    <appMagic:includeProperty name="PaddingTop" defaultValue="5" />
    <appMagic:includeProperty name="PaddingRight" defaultValue="5" />
    <appMagic:includeProperty name="PaddingBottom" defaultValue="5" />
    <appMagic:includeProperty name="PaddingLeft" defaultValue="5" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="VerticalAlign" defaultValue="%VerticalAlign.RESERVED%.Middle" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="160" phoneDefaultValue="280" webDefaultValue="85" />
    <appMagic:includeProperty name="Height" defaultValue="40" phoneDefaultValue="70" webDefaultValue="32" />
    <appMagic:includeProperty name="TabIndex" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="5" />
    <appMagic:includeProperty name="minimumWidth" defaultValue="5" />
  </appMagic:includeProperties>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Popular" priority="80" />
    <appMagic:category name="Input" priority="10" />
    <appMagic:category name="ClassicControls" priority="10" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Text" />
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Padding">
        <appMagic:property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <appMagic:property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <appMagic:property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <appMagic:property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Color" showInFloatie="true" showInCommandBar="true" />
        <appMagic:property name="Fill" showInFloatie="true" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Radius">
        <appMagic:property name="RadiusTopLeft" />
        <appMagic:property name="RadiusTopRight" />
        <appMagic:property name="RadiusBottomLeft" />
        <appMagic:property name="RadiusBottomRight" />
      </appMagic:propertyGroup>
      <appMagic:property name="Font" displayType="FontEnum" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="Size" labelOverride="##FontSize_Property##" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="FontWeight" displayType="EnumIcon" itemsOrder="Bold;Semibold;Normal;Lighter" showInCommandBar="true" />
      <appMagic:propertyGroup name="Style">
        <appMagic:property name="Italic" displayType="ToggleButton" />
        <appMagic:property name="Underline" displayType="ToggleButton" />
        <appMagic:property name="Strikethrough" displayType="ToggleButton" />
      </appMagic:propertyGroup>
      <appMagic:property name="Align" displayType="EnumButtons" itemsOrder="Left;Center;Right;Justify" labelOverride="##FontAlign_Property##" showInFloatie="true" showInCommandBar="true" floatieDisplayType="FaceplateIconEnum" />
      <appMagic:property name="VerticalAlign" displayType="EnumIcon" itemsOrder="Top;Middle;Bottom" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="AutoDisableOnSelect" />
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledColor" />
        <appMagic:property name="DisabledFill" />
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="PressedColor">
        <appMagic:property name="PressedColor" />
        <appMagic:property name="PressedFill" />
        <appMagic:property name="PressedBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverColor" />
        <appMagic:property name="HoverFill" />
        <appMagic:property name="HoverBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.0.1">
    <!-- Accessibility fixes for KO template -->
  </appMagic:conversion>
  <appMagic:conversion from="2.0.1" to="2.1.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <!-- Adding showInCommandBar flag -->
  </appMagic:conversion>
</widget>
<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/toggleSwitch" name="toggleSwitch" jsClass="AppMagic.Controls.ToggleSwitch" version="2.1.0" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[toggleSwitch
      Control description here.]]></description>
  <requires>
    <require type="css" src="css/toggleSwitch.css" />
    <require type="javascript" src="js/toggleSwitch.js" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoBorders="true" autoFocusedBorders="true" autoPointerViewState="true" autoDisabledViewState="true" isVersionFlexible="true" />
  <appMagic:accessibilityChecks controlIsInteractive="true">
    <appMagic:accessibilityCheck type="StateIndicationTextNeeded" property="TrueText" />
    <appMagic:accessibilityCheck type="StateIndicationTextNeeded" property="FalseText" />
    <appMagic:accessibilityCheck type="HelpfulControlSettingNeeded" property="ShowLabel" />
  </appMagic:accessibilityChecks>
  <content><![CDATA[
    <div class="appmagic-toggleSwitch-control"
      data-bind="{
        event: {
          click: offOnRailClick
        }
      }">
      <div
        class="appmagic-toggleSwitch-label"
        data-bind="{
          attr: {
            id: labelId,
            'aria-hidden': viewState.displayMode() !== AppMagic.Constants.DisplayMode.View,
            'aria-disabled': viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled
          },
          text: properties.Value() ? properties.TrueText : properties.FalseText,
          style: {
            color: autoProperties.Color,
            fontFamily: properties.Font,
            fontSize: properties.Size,
            fontStyle: properties.Italic,
            fontWeight: properties.FontWeight,
            paddingLeft: properties.LabelPaddingLeft() + 'px',
            paddingRight: properties.LabelPaddingRight() + 'px',
            height: AppMagic.Controls.converters.pxVerticalConverter.view(toggleHeight),
            width: AppMagic.Controls.converters.pxHorizontalConverter.view(labelWidth),
            visibility: showLabelText ? '' : 'hidden'
          },
          css: {
            left: properties.TextPosition() === 'left',
            right: properties.TextPosition() === 'right',
            strikethrough: properties.Strikethrough,
            underline: properties.Underline,
            readOnly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View
          },
          visible: hasLabel
        }">
      </div>
      <div
        class="appmagic-toggleSwitch no-focus-outline"
        role="switch"
        data-bind="{
            style: {
              height: AppMagic.Controls.converters.pxVerticalConverter.view(toggleHeight),
              width: AppMagic.Controls.converters.pxHorizontalConverter.view(toggleWidth),
              borderRadius: AppMagic.Controls.converters.pxVerticalConverter.view(toggleHeight / 2),
            },
            attr: {
              title: properties.Tooltip() || null,
              'aria-label': properties.AccessibleLabel() || null,
              'aria-describedby': properties.ShowLabel() ? labelId : null,
              'aria-checked': properties.Value() ? 'true' : 'false',
              'aria-disabled': viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit
            },
            disable: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled || viewState.displayMode() === AppMagic.Constants.DisplayMode.View,
            shortcut: {
              provider: shortcutProvider
            },
            css: {
              disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled,
              readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,
              left: properties.TextPosition() === 'right',
              right: properties.TextPosition() === 'left'
            },
            visible: viewState.displayMode() !== AppMagic.Constants.DisplayMode.View
          }" >
          <div
            class="appmagic-toggleSwitch-off"
              data-bind="{
                style: {
                  backgroundColor: trueFill,
                  left: AppMagic.Controls.converters.pxHorizontalConverter.view(switchOff.left()),
                  top: AppMagic.Controls.converters.pxVerticalConverter.view(switchOff.top()),
                  width: AppMagic.Controls.converters.pxHorizontalConverter.view(switchOff.width()),
                  height: AppMagic.Controls.converters.pxVerticalConverter.view(switchOff.height()),
                },
                css: {
                  disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled
                }
              }">
          </div>
          <div
            class="appmagic-toggleSwitch-on"
              data-bind="{
                style: {
                  backgroundColor: falseFill,
                  left: AppMagic.Controls.converters.pxHorizontalConverter.view(switchOn.left()),
                  top: AppMagic.Controls.converters.pxVerticalConverter.view(switchOn.top()),
                  width: AppMagic.Controls.converters.pxHorizontalConverter.view(switchOn.width()),
                  height: AppMagic.Controls.converters.pxVerticalConverter.view(switchOn.height()),
                },
                css: {
                  disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled
                }
              }">
          </div>
          <div
            class="appmagic-toggleSwitch-handle"
            touch-action="none"
            aria-hidden="true"
              data-bind="{
                style: {
                  backgroundColor: properties.HandleFill,
                  left: AppMagic.Controls.converters.pxHorizontalConverter.view(handle.left()),
                  top: AppMagic.Controls.converters.pxVerticalConverter.view(handle.top()),
                  width: AppMagic.Controls.converters.pxHorizontalConverter.view(handle.width()),
                  height: AppMagic.Controls.converters.pxVerticalConverter.view(handle.height()),
                  borderRadius: AppMagic.Controls.converters.pxHorizontalConverter.view(handle.width() / 2),
                },
                css: {
                  disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled
                },
                event: { pointerdown: onMouseDown }
              }">
          </div>
      </div>
    </div>
    ]]></content>
  <properties>
    <property name="Value" localizedName="##toggleSwitch_Value##" datatype="Boolean" editable="true" defaultValue="false" direction="out" isPrimaryOutputProperty="true" supportsAutomation="true">
      <appMagic:category>data</appMagic:category>
    </property>
    <property name="Default" localizedName="##toggleSwitch_Default##" datatype="Boolean" defaultValue="false" editable="true" direction="in">
      <title>Default value</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:helperUI>boolean</appMagic:helperUI>
      <appMagic:displayName>##toggleSwitch_Default_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_Default_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Reset" localizedName="##commonProperties_Reset##" datatype="Boolean" defaultValue="false" direction="in">
      <title>Reset</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip>
    </property>
    <property name="OnCheck" localizedName="##toggleSwitch_OnCheck##" datatype="Boolean" defaultValue="false" direction="in">
      <appMagic:category>behavior</appMagic:category>
      <appMagic:displayName>##toggleSwitch_OnCheck_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##checkbox_OnCheck_Tooltip##</appMagic:tooltip>
    </property>
    <property name="OnUncheck" localizedName="##toggleSwitch_OnUncheck##" datatype="Boolean" defaultValue="false" direction="in">
      <appMagic:category>behavior</appMagic:category>
      <appMagic:displayName>##toggleSwitch_OnUncheck_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##checkbox_OnUncheck_Tooltip##</appMagic:tooltip>
    </property>
    <property name="HandleFill" localizedName="##toggleSwitch_HandleFill##" datatype="Color" defaultValue="RGBA(69, 69, 69, 1)" isExpr="true" converter="argbConverter">
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##toggleSwitch_HandleFill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_HandleFill_Tooltip##</appMagic:tooltip>
    </property>
    <property name="TextPosition" localizedName="##toggleSwitch_TextPosition##" datatype="TextPosition" defaultValue="%TextPosition.RESERVED%.Right" isExpr="true">
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##toggleSwitch_TextPosition_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_TextPosition_Tooltip##</appMagic:tooltip>
    </property>
    <property name="FalseText" localizedName="##toggleSwitch_FalseText##" datatype="String" defaultValue="##toggleSwitch_DefaultValue_FalseText##">
      <appMagic:displayName>##toggleSwitch_FalseText_DisplayName##</appMagic:displayName>
    </property>
    <property name="TrueText" localizedName="##toggleSwitch_TrueText##" datatype="String" defaultValue="##toggleSwitch_DefaultValue_TrueText##">
      <appMagic:displayName>##toggleSwitch_TrueText_DisplayName##</appMagic:displayName>
    </property>
    <property name="FalseFill" localizedName="##toggleSwitch_FalseFill##" datatype="Color" defaultValue="RGBA(186, 186, 186, 1)" isExpr="true" converter="argbConverter">
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##toggleSwitch_FalseFill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_FalseFill_Tooltip##</appMagic:tooltip>
    </property>
    <property name="FalseHoverFill" localizedName="##toggleSwitch_FalseHoverFill##" datatype="Color" isExpr="true" defaultValue="ColorFade(Self.FalseFill, 15%)" converter="argbConverter">
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##toggleSwitch_FalseHoverFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="ShowLabel" localizedName="##toggleSwitch_ShowLabel##" datatype="Boolean" defaultValue="true">
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##toggleSwitch_ShowLabel_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_ShowLabel_Tooltip##</appMagic:tooltip>
    </property>
    <property name="LabelPaddingLeft" localizedName="##toggleSwitch_LabelPaddingLeft##" datatype="Number" defaultValue="5">
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##toggleSwitch_LabelPaddingLeft_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_LabelPaddingLeft_Tooltip##</appMagic:tooltip>
    </property>
    <property name="LabelPaddingRight" localizedName="##toggleSwitch_LabelPaddingRight##" datatype="Number" defaultValue="5">
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##toggleSwitch_LabelPaddingRight_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_LabelPaddingRight_Tooltip##</appMagic:tooltip>
    </property>
    <property name="TrueFill" localizedName="##toggleSwitch_TrueFill##" datatype="Color" defaultValue="RGBA(0, 176, 240, 1)" isExpr="true" converter="argbConverter">
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##toggleSwitch_TrueFill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##toggleSwitch_TrueFill_Tooltip##</appMagic:tooltip>
    </property>
    <property name="TrueHoverFill" localizedName="##toggleSwitch_TrueHoverFill##" datatype="Color" isExpr="true" defaultValue="ColorFade(Self.TrueFill, 15%)" converter="argbConverter">
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##toggleSwitch_TrueHoverFill_DisplayName##</appMagic:displayName>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Data -->
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="BorderColor" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="RGBA(56, 56, 56, 1)" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="ColorFade(Self.BorderColor, -15%)" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="ColorFade(Self.BorderColor, 15%)" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" />
    <appMagic:includeProperty name="Height" defaultValue="28" phoneDefaultValue="49" webDefaultValue="22" />
    <appMagic:includeProperty name="Width" defaultValue="92" phoneDefaultValue="154" webDefaultValue="40" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="DisplayMode" />
    <appMagic:includeProperty name="TabIndex" />
    <appMagic:includeProperty name="Italic" />
    <appMagic:includeProperty name="Strikethrough" />
    <appMagic:includeProperty name="FontWeight" />
    <appMagic:includeProperty name="Underline" />
    <appMagic:includeProperty name="Color" defaultValue="RGBA(70, 68, 64, 1)" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="Font" />
    <appMagic:includeProperty name="Size" defaultValue="14" phoneDefaultValue="24" />
    <!-- Behavior -->
    <!-- TASK: 85476: Do Behavior properties make sense as input only? -->
    <appMagic:includeProperty name="OnSelect" direction="in" />
    <appMagic:includeProperty name="OnChange" direction="in" isPrimaryInputProperty="true" isPrimaryBehaviorProperty="true" />
    <!-- Hidden -->
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="5" />
    <appMagic:includeProperty name="minimumWidth" defaultValue="5" />
  </appMagic:includeProperties>
  <!--Property Dependencies -->
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="Default" output="Value" />
    <appMagic:propertyDependency input="Reset" output="Value" />
  </appMagic:propertyDependencies>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Input" priority="100" />
    <appMagic:category name="ClassicControls" priority="100" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Default" />
      <appMagic:property name="ShowLabel" />
      <appMagic:property name="TextPosition" />
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="FalseFill" />
      <appMagic:property name="TrueFill" />
      <appMagic:property name="HandleFill" />
      <appMagic:property name="Color" showInFloatie="true" showInCommandBar="true" />
      <appMagic:property name="Font" displayType="FontEnum" showInFloatie="true" />
      <appMagic:property name="Size" labelOverride="##FontSize_Property##" showInFloatie="true" />
      <appMagic:property name="FontWeight" displayType="EnumIcon" itemsOrder="Bold;Semibold;Normal;Lighter" />
      <appMagic:propertyGroup name="Style">
        <appMagic:property name="Italic" displayType="ToggleButton" />
        <appMagic:property name="Underline" displayType="ToggleButton" />
        <appMagic:property name="Strikethrough" displayType="ToggleButton" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="TrueHoverFill" />
      <appMagic:property name="FalseHoverFill" />
      <appMagic:propertyGroup name="PressedColor">
        <appMagic:property name="PressedBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="Tooltip" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.0.1">
    <!-- KO template changes for accessibility fixes -->
  </appMagic:conversion>
  <appMagic:conversion from="2.0.1" to="2.1.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
</widget>
{"FormatVersion": "0.24", "Header": {"AnalysisOptions": {"DataflowAnalysisEnabled": true, "DataflowAnalysisFlagStateToggledByUser": false}, "DocVersion": "1.346", "MinVersionToLoad": "1.331", "MSAppStructureVersion": "2.0"}, "Properties": {"AppCopilotSchemaName": "", "AppCreationSource": "AppFromData", "AppDescription": "", "AppPreviewFlagsMap": {"adaptivepaging": false, "aibuilderserviceenrollment": false, "allowmultiplescreensincanvaspages": false, "appinsightserrortracing": false, "appinstrumentationcorrelationtracing": false, "autocreateenvironmentvariables": false, "behaviorpropertyui": true, "blockmovingcontrol": true, "cdsdataformatting": false, "classiccontrols": false, "commentgeneratedformulasv2": false, "consistentreturnschemafortabularfunctions": true, "copyandmerge": false, "dataflowanalysisenabled": true, "datatablev2control": true, "dataverseactionsenabled": true, "delaycontrolrendering": true, "delayloadscreens": true, "disablebehaviorreturntypecheck": false, "disablecdsfileandlargeimage": false, "disablefallbacktopayamlv2": false, "disableruntimepolicies": false, "dynamicschema": false, "enableappembeddingux": false, "enablecanvasappruntimecopilot": true, "enablecomponentnamemaps": false, "enablecomponentscopeoldbehavior": false, "enablecopilotanswercontrol": true, "enablecopilotcontrol": true, "enablecreateaformula": false, "enabledataverseoffline": false, "enableeditcacherefreshfrequency": false, "enableeditinmcs": false, "enableexcelonlinebusinessv2connector": true, "enableideaspanel": true, "enableideaspanelbyexample": false, "enablelegacybarcodescanner": false, "enablelegacydatatable": false, "enableonstart": true, "enableonstartnavigate": false, "enablepcfmoderndatasets": true, "enablerowscopeonetonexpand": false, "enablerpawarecomponentdependency": true, "enablesaveloadcleardataonweb": true, "enableupdateifdelegation": true, "errorhandling": true, "expandedsavedatasupport": true, "exportimportcomponents2": true, "externalmessage": false, "fluentv9controls": false, "fluentv9controlspreview": false, "formuladataprefetch": true, "formularepair": false, "generatedebugpublishedapp": false, "herocontrols": false, "improvedtabstopbehavior": false, "isemptyrequirestableargument": true, "keeprecentscreensloaded": false, "loadcomponentdefinitionsondemand": true, "longlivingcache": false, "mobilenativerendering": false, "nativecdsexperimental": true, "offlineprofilegenerationemitscolumns": false, "onegrid": false, "optimizedforteamsmeeting": false, "optimizestartscreenpublishedappload": false, "packagemodernruntime": false, "pdffunction": false, "powerfxdecimal": false, "powerfxv1": false, "preferpayamlv2inux": false, "primaryoutputpropertycoerciondeprecated": true, "proactivecontrolrename": false, "projectionmapping": true, "reliableconcurrent": true, "reservedkeywords": false, "rtlinstudiopreview": false, "rtlsupport": false, "sharepointselectsenabled": false, "showclassicthemes": false, "smartemaildatacard": false, "supportcolumnnamesasidentifiers": true, "tabledoesntwraprecords": true, "usedisplaynamemetadata": true, "useenforcesavedatalimits": true, "useexperimentalcdsconnector": true, "useexperimentalsqlconnector": true, "useguiddatatypes": true, "usenonblockingonstartrule": true, "userdefinedfunctions": false, "userdefinedtypes": false, "webbarcodescanner": false, "zeroalltabindexes": true}, "Author": "", "BindingErrorCount": 3, "ConnectionString": "", "ContainsThirdPartyPcfControls": false, "DefaultConnectedDataSourceMaxGetRowsCount": 500, "DocumentAppType": "Phone", "DocumentLayoutHeight": 640, "DocumentLayoutLockOrientation": false, "DocumentLayoutMaintainAspectRatio": true, "DocumentLayoutOrientation": "landscape", "DocumentLayoutScaleToFit": true, "DocumentLayoutWidth": 1136, "DocumentType": "App", "EnableInstrumentation": false, "FileID": "07b2c37b-5985-41dd-a5f9-074665fce5d2", "Id": "51ea7194-eb66-4646-8df6-5b9ca4363313", "LocalDatabaseReferences": "", "ManualOfflineProfileId": "", "Name": "RW1wbG95ZWUgVGltZSBPZmYgLSBJbmRpYW5h.msapp", "OriginatingVersion": "1.345", "ParserErrorCount": 0, "ShowStatusBar": false}, "PublishInfo": {"AppName": "Employee Time Off - Indiana", "BackgroundColor": "rgba(211, 66, 9, 1)", "IconColor": "RGBA(255,255,255,1)", "IconName": "Clock", "LogoFileName": "logo.jpg", "PublishDataLocally": false, "PublishResourcesLocally": false, "PublishTarget": "player", "UserLocale": "en-US"}, "ScreenOrder": ["AbsencesByDate", "DateRestriction", "EditRecordScreen", "EnterData", "FirstPage", "Screen1", "Screen2"]}
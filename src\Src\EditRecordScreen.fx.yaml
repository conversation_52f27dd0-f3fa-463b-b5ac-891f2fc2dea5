"EditRecordScreen As screen.'phoneLayout_HeaderAndForm_ver3.0'":
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)

    RectQuickActionBar5 As rectangle:
        BorderColor: =RGBA(131, 24, 75, 1)
        Fill: =RGBA(211, 66, 9, 1)
        Height: =49.58
        Width: =Parent.Width
        ZIndex: =1

    IconCancel4 As icon.Cancel:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =62
        Icon: =Icon.Cancel
        OnSelect: =Back()
        PaddingBottom: =22
        PaddingLeft: =22
        PaddingRight: =22
        PaddingTop: =10
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Cancel item"
        Width: =100
        ZIndex: =2

    IconAccept4 As icon.Check:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =62
        Icon: =Icon.Check
        OnSelect: |-
            =SubmitForm(EditForm4);
            Back()
        PaddingBottom: =22
        PaddingLeft: =22
        PaddingRight: =22
        PaddingTop: =10
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Submit item"
        Width: =100
        X: =Parent.Width - Self.Width
        ZIndex: =3

    LblAppName5 As label:
        Color: =RGBA(255, 255, 255, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =49
        Size: =15.21
        Text: ="Edit the Record"
        Width: =Parent.Width - IconCancel4.Width - IconAccept4.Width
        Wrap: =false
        X: =IconCancel4.X + IconCancel4.Width
        ZIndex: =4

    EditForm4 As form:
        BorderColor: =RGBA(131, 24, 75, 1)
        DataSource: =TimeOffIndiana
        Height: =Parent.Height - EditForm4.Y
        Item: =gblRecordToEdit
        NumberOfColumns: =2
        Width: =Parent.Width
        Y: =RectQuickActionBar5.Height
        ZIndex: =5

        Scheduled_DataCard1 As typedDataCard.toggleEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Scheduled"
            Default: =ThisItem.Scheduled
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Scheduled)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =DataCardValue1.Value
            Width: =568
            X: =0
            Y: =3
            ZIndex: =1

            DataCardKey1 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue1 As toggleSwitch:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DisplayMode: =Parent.DisplayMode
                FalseFill: =RGBA(128, 130, 133, 1)
                FalseText: ="No"
                HandleFill: =RGBA(255, 255, 255, 1)
                Height: =27.61
                Size: =11.83
                Tooltip: =Parent.DisplayName
                TrueFill: =RGBA(211, 66, 9, 1)
                TrueText: ="Yes"
                Width: =154
                X: =30
                Y: =DataCardKey1.Y + DataCardKey1.Height + 5
                ZIndex: =2

            ErrorMessage13 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue1.Y + DataCardValue1.Height
                ZIndex: =3

            StarVisible13 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey1.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey1.Y
                ZIndex: =4

        "'Requested Time_DataCard2' As typedDataCard.dateTimeEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="createdtime"
            Default: =ThisItem.'Requested Time'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Requested Time')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =If(Not IsBlank(DateValue6.SelectedDate), DateTime(Year(DateValue6.SelectedDate), Month(DateValue6.SelectedDate), Day(DateValue6.SelectedDate), Value(HourValue6.Selected.Value), Value(MinuteValue6.Selected.Value), 0))
            Width: =568
            X: =1
            Y: =3
            ZIndex: =1

            DataCardKey28 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =3
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DateValue6 As datepicker:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                CalendarHeaderFill: =RGBA(211, 66, 9, 1)
                Color: =RGBA(0, 0, 0, 1)
                DefaultDate: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                EndYear: =Year(Today())+100
                Height: =39.44
                HoverDateFill: =RGBA(255, 211, 205, 1)
                IconBackground: =RGBA(211, 66, 9, 1)
                IsEditable: =true
                PaddingBottom: =0
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                SelectedDateFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                StartYear: =1899
                Tooltip: =Parent.DisplayName
                Width: =(Parent.Width - 60) / 2
                X: =30
                Y: =DataCardKey28.Y + DataCardKey28.Height + 5
                ZIndex: =2

            HourValue6 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Hour(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =DateValue6.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =DateValue6.Width / 2 - 10
                X: =DateValue6.X + DateValue6.Width + 10
                Y: =DateValue6.Y
                ZIndex: =3

            Separator8 As label:
                Align: =Align.Center
                BorderColor: =RGBA(131, 24, 75, 1)
                Color: =RGBA(0, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Bold
                Height: =HourValue6.Height
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =21
                Text: |-
                    =":"
                Width: =10
                Wrap: =false
                X: =HourValue6.X + HourValue6.Width
                Y: =HourValue6.Y
                ZIndex: =4

            MinuteValue6 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Minute(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =HourValue6.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =HourValue6.Width
                X: =HourValue6.X + HourValue6.Width + Separator8.Width
                Y: =HourValue6.Y
                ZIndex: =5

            ErrorMessage21 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =HourValue6.Y + HourValue6.Height
                ZIndex: =6

            StarVisible21 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey28.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey28.Y
                ZIndex: =7

        "'Created BY_DataCard2' As typedDataCard.comboBoxEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="CreatedBY"
            Default: =ThisItem.'Created BY'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,CreatedBY)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =DataCardValue6.Selected
            Visible: =false
            Width: =568
            X: =0
            Y: =4
            ZIndex: =1

            DataCardKey26 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =21
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue6 As combobox:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                DefaultSelectedItems: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayFields: =["Claims"]
                DisplayMode: =Parent.DisplayMode
                Height: =70
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =Choices([@TimeOffIndiana].CreatedBY)
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SearchFields: =["Claims"]
                SearchItems: =Choices(TimeOffIndiana.CreatedBY,DataCardValue6.SearchText)
                SelectionFill: =RGBA(211, 66, 9, 1)
                SelectMultiple: =false
                Size: =21
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardKey26.Y + DataCardKey26.Height + 5
                ZIndex: =2

            ErrorMessage19 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue6.Y + DataCardValue6.Height
                ZIndex: =3

            StarVisible19 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey26.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey26.Y
                ZIndex: =4

        canceled_DataCard1 As typedDataCard.toggleEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="canceled"
            Default: =ThisItem.canceled
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,canceled)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =DataCardValue23.Value
            Width: =568
            X: =1
            Y: =4
            ZIndex: =1

            DataCardKey30 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue23 As toggleSwitch:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DisplayMode: =Parent.DisplayMode
                FalseFill: =RGBA(128, 130, 133, 1)
                HandleFill: =RGBA(255, 255, 255, 1)
                Height: =27.61
                Size: =11.83
                Tooltip: =Parent.DisplayName
                TrueFill: =RGBA(211, 66, 9, 1)
                Width: =154
                X: =30
                Y: =DataCardKey30.Y + DataCardKey30.Height + 5
                ZIndex: =2

            ErrorMessage24 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue23.Y + DataCardValue23.Height
                ZIndex: =3

            StarVisible24 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey30.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey30.Y
                ZIndex: =4

        Name_DataCard3 As typedDataCard.textualEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Name"
            Default: =ThisItem.Name
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Name)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            MaxLength: =DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Name)
            Required: =false
            Update: =DataCardValue20.Text
            Width: =568
            X: =0
            Y: =0
            ZIndex: =6

            DataCardKey25 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue20 As text:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                BorderThickness: =2
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DelayOutput: =true
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                FocusedBorderThickness: =4
                Height: =39.44
                HoverBorderColor: =RGBA(131, 24, 75, 1)
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                MaxLength: =Parent.MaxLength
                PaddingLeft: =5
                RadiusBottomLeft: =0
                RadiusBottomRight: =0
                RadiusTopLeft: =0
                RadiusTopRight: =0
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardKey25.Y + DataCardKey25.Height + 5
                ZIndex: =2

            ErrorMessage18 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue20.Y + DataCardValue20.Height
                ZIndex: =3

            StarVisible18 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey25.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey25.Y
                ZIndex: =4

        "'Leave From_DataCard3' As typedDataCard.dateTimeEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="LeaveFrom"
            Default: =ThisItem.'Leave From'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave From')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =If(Not IsBlank(DateValue4.SelectedDate), DateTime(Year(DateValue4.SelectedDate), Month(DateValue4.SelectedDate), Day(DateValue4.SelectedDate), Value(HourValue4.Selected.Value), Value(MinuteValue4.Selected.Value), 0))
            Width: =568
            X: =0
            Y: =1
            ZIndex: =6

            DataCardKey21 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DateValue4 As datepicker:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                CalendarHeaderFill: =RGBA(211, 66, 9, 1)
                Color: =RGBA(0, 0, 0, 1)
                DefaultDate: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                EndYear: =Year(Today())+100
                Height: =39.44
                HoverDateFill: =RGBA(255, 211, 205, 1)
                IconBackground: =RGBA(211, 66, 9, 1)
                IsEditable: =true
                PaddingBottom: =0
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                SelectedDateFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                StartYear: =1899
                Tooltip: =Parent.DisplayName
                Width: =(Parent.Width - 60) / 2
                X: =30
                Y: =DataCardKey21.Y + DataCardKey21.Height + 5
                ZIndex: =2

            HourValue4 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Hour(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =DateValue4.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =DateValue4.Width / 2 - 10
                X: =DateValue4.X + DateValue4.Width + 10
                Y: =DateValue4.Y
                ZIndex: =3

            Separator6 As label:
                Align: =Align.Center
                BorderColor: =RGBA(131, 24, 75, 1)
                Color: =RGBA(0, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Bold
                Height: =HourValue4.Height
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =21
                Text: |-
                    =":"
                Width: =10
                Wrap: =false
                X: =HourValue4.X + HourValue4.Width
                Y: =HourValue4.Y
                ZIndex: =4

            MinuteValue4 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Minute(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =HourValue4.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =HourValue4.Width
                X: =HourValue4.X + HourValue4.Width + Separator6.Width
                Y: =HourValue4.Y
                ZIndex: =5

            ErrorMessage14 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =HourValue4.Y + HourValue4.Height
                ZIndex: =6

            StarVisible14 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey21.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey21.Y
                ZIndex: =7

        "'Leave To_DataCard3' As typedDataCard.dateTimeEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="LeaveTo"
            Default: =ThisItem.'Leave To'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave To')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =If(Not IsBlank(DateValue5.SelectedDate), DateTime(Year(DateValue5.SelectedDate), Month(DateValue5.SelectedDate), Day(DateValue5.SelectedDate), Value(HourValue5.Selected.Value), Value(MinuteValue5.Selected.Value), 0))
            Width: =568
            X: =1
            Y: =1
            ZIndex: =6

            DataCardKey22 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DateValue5 As datepicker:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                CalendarHeaderFill: =RGBA(211, 66, 9, 1)
                Color: =RGBA(0, 0, 0, 1)
                DefaultDate: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                EndYear: =Year(Today())+100
                Height: =39.44
                HoverDateFill: =RGBA(255, 211, 205, 1)
                IconBackground: =RGBA(211, 66, 9, 1)
                IsEditable: =true
                PaddingBottom: =0
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                SelectedDateFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                StartYear: =1899
                Tooltip: =Parent.DisplayName
                Width: =(Parent.Width - 60) / 2
                X: =30
                Y: =DataCardKey22.Y + DataCardKey22.Height + 5
                ZIndex: =2

            HourValue5 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Hour(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =DateValue5.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =DateValue5.Width / 2 - 10
                X: =DateValue5.X + DateValue5.Width + 10
                Y: =DateValue5.Y
                ZIndex: =3

            Separator7 As label:
                Align: =Align.Center
                BorderColor: =RGBA(131, 24, 75, 1)
                Color: =RGBA(0, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Bold
                Height: =HourValue5.Height
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =21
                Text: |-
                    =":"
                Width: =10
                Wrap: =false
                X: =HourValue5.X + HourValue5.Width
                Y: =HourValue5.Y
                ZIndex: =4

            MinuteValue5 As dropdown:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                Default: =Text(Minute(Parent.Default),"00")
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =HourValue5.Height
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                Items: =["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"]
                PaddingBottom: =5
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PaddingRight: =5
                PaddingTop: =5
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =HourValue5.Width
                X: =HourValue5.X + HourValue5.Width + Separator7.Width
                Y: =HourValue5.Y
                ZIndex: =5

            ErrorMessage15 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =HourValue5.Y + HourValue5.Height
                ZIndex: =6

            StarVisible15 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey22.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey22.Y
                ZIndex: =7

        Location_DataCard2 As typedDataCard.textualEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Location"
            Default: ="Customer Service Unit"
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Location)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            MaxLength: =DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Location)
            Required: =false
            Update: =DataCardValue18.Text
            Visible: =false
            Width: =568
            X: =1
            Y: =1
            ZIndex: =6

            DataCardKey23 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue18 As text:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                BorderThickness: =2
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DelayOutput: =true
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                FocusedBorderThickness: =4
                Height: =39.44
                HoverBorderColor: =RGBA(131, 24, 75, 1)
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                MaxLength: =Parent.MaxLength
                PaddingLeft: =5
                RadiusBottomLeft: =0
                RadiusBottomRight: =0
                RadiusTopLeft: =0
                RadiusTopRight: =0
                Size: =21
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardKey23.Y + DataCardKey23.Height + 5
                ZIndex: =2

            ErrorMessage16 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue18.Y + DataCardValue18.Height
                ZIndex: =3

            StarVisible16 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey23.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey23.Y
                ZIndex: =4

        Approval_DataCard3 As typedDataCard.toggleEditCard:
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="Approval"
            Default: =ThisItem.Approval
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Approval)
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =DataCardValue19.Value
            Width: =568
            X: =0
            Y: =2
            ZIndex: =6

            DataCardKey24 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue19 As toggleSwitch:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                Color: =RGBA(0, 0, 0, 1)
                Default: =Parent.Default
                DisplayMode: =Parent.DisplayMode
                FalseFill: =RGBA(128, 130, 133, 1)
                FalseText: ="No"
                HandleFill: =RGBA(255, 255, 255, 1)
                Height: =27.61
                Size: =11.83
                Tooltip: =Parent.DisplayName
                TrueFill: =RGBA(211, 66, 9, 1)
                TrueText: ="Yes"
                Width: =154
                X: =30
                Y: =DataCardKey24.Y + DataCardKey24.Height + 5
                ZIndex: =2

            ErrorMessage17 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue19.Y + DataCardValue19.Height
                ZIndex: =3

            StarVisible17 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey24.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey24.Y
                ZIndex: =4

        "'Leave Type_DataCard3' As typedDataCard.comboBoxEditCard":
            BorderColor: =RGBA(131, 24, 75, 1)
            BorderStyle: =BorderStyle.Solid
            DataField: ="LeaveType"
            Default: =ThisItem.'Leave Type'
            DisplayMode: =Parent.DisplayMode
            DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave Type')
            Fill: =RGBA(0, 0, 0, 0)
            Height: =50
            Required: =false
            Update: =DataCardValue22.Selected
            Width: =568
            X: =1
            Y: =2
            ZIndex: =6

            DataCardKey27 As label:
                AutoHeight: =true
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =48
                Size: =11.83
                Text: =Parent.DisplayName
                Width: =Parent.Width - 60
                Wrap: =false
                X: =30
                Y: =10
                ZIndex: =1

            DataCardValue22 As combobox:
                BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                ChevronBackground: =RGBA(211, 66, 9, 1)
                ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                ChevronHoverFill: =RGBA(255, 255, 255, 1)
                DefaultSelectedItems: =Parent.Default
                DisabledBorderColor: =RGBA(166, 166, 166, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                DisabledFill: =RGBA(244, 244, 244, 1)
                DisplayMode: =Parent.DisplayMode
                Height: =39.44
                HoverColor: =RGBA(0, 0, 0, 1)
                HoverFill: =RGBA(255, 211, 205, 1)
                InputTextPlaceholder: ="Leave Type"
                Items: =Choices([@TimeOffIndiana].'Leave Type')
                PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                PressedColor: =RGBA(255, 255, 255, 1)
                PressedFill: =RGBA(131, 24, 75, 1)
                SelectionFill: =RGBA(211, 66, 9, 1)
                SelectMultiple: =false
                Size: =11.83
                Tooltip: =Parent.DisplayName
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardKey27.Y + DataCardKey27.Height + 5
                ZIndex: =2

            ErrorMessage20 As label:
                AutoHeight: =true
                Color: =RGBA(168, 0, 0, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                FontWeight: =FontWeight.Semibold
                Height: =10
                Live: =Live.Assertive
                PaddingBottom: =0
                PaddingLeft: =0
                PaddingRight: =0
                PaddingTop: =0
                Size: =24
                Text: =Parent.Error
                Visible: =Parent.DisplayMode=DisplayMode.Edit
                Width: =Parent.Width - 60
                X: =30
                Y: =DataCardValue22.Y + DataCardValue22.Height
                ZIndex: =3

            StarVisible20 As label:
                Align: =Align.Center
                Color: =RGBA(131, 24, 75, 1)
                DisabledColor: =RGBA(166, 166, 166, 1)
                Height: =DataCardKey27.Height
                Size: =21
                Text: ="*"
                Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                Width: =30
                Wrap: =false
                Y: =DataCardKey27.Y
                ZIndex: =4

    check_availability_1 As button:
        BorderThickness: =0
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(0, 0, 0, 0.15)
        FontWeight: =FontWeight.Semibold
        Height: =39.44
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Navigate(
              AbsencesByDate, 
              ScreenTransition.Fade
            )
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =11.27
        Text: ="Check Availability"
        Width: =280
        X: =272
        Y: =5
        ZIndex: =6


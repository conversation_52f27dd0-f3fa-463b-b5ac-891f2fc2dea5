{"ControlStates": {"875f9c74-f155-411c-a996-0a877682c408": {"AllowAccessToGlobals": true, "ControlPropertyState": [], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "875f9c74-f155-411c-a996-0a877682c408", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [], "StyleName": "", "Type": "ControlInfo"}, "galCalendar_1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Items", "WrapCount", "Selectable", "TemplateSize", "TemplateMaximum<PERSON>idth", "TemplatePadding", "MaxTemplateSize", "Layout", "Transition", "ShowNavigation", "NavigationStep", "ShowScrollbar", "SelectionTracksMove", "Reset", "AutoHeight", "DelayItemLoading", "LoadingSpinner", "LoadingSpinnerColor", "AccessibleLabel", "ContentLanguage", "DisplayMode", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderThickness", "BorderStyle", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "Visible", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight", "#CopilotOverlayLabel", "#CopilotOverlayVisible", "ZIndex"], "GalleryTemplateChildName": "875f9c74-f155-411c-a996-0a877682c408", "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "galCalendar_1", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "WrapCount", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Selectable", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Reset", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateMaximum<PERSON>idth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplatePadding", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MaxTemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Layout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Transition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ShowNavigation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "NavigationStep", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ShowScrollbar", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTracksMove", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DelayItemLoading", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "#CopilotOverlayLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "#CopilotOverlayVisible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "GalleryDayHeaders": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Height", "Items", "TemplatePadding", "TemplateSize", "<PERSON><PERSON><PERSON>", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "X", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Y", "IsLockable": false, "NameMapSourceSchema": "?"}, "ZIndex", "<PERSON><PERSON><PERSON>", "WrapCount", "Selectable", "TemplateMaximum<PERSON>idth", "MaxTemplateSize", "Layout", "Transition", "ShowNavigation", "NavigationStep", "ShowScrollbar", "SelectionTracksMove", "Reset", "AutoHeight", "DelayItemLoading", "LoadingSpinner", "LoadingSpinnerColor", "AccessibleLabel", "ContentLanguage", "DisplayMode", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderThickness", "BorderStyle", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Visible", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight", "#CopilotOverlayVisible"], "GalleryTemplateChildName": "GalleryDayHeaderstemplate1", "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "GalleryDayHeaders", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "WrapCount", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Selectable", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Reset", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplatePadding", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateMaximum<PERSON>idth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MaxTemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Layout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Transition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ShowNavigation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "NavigationStep", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ShowScrollbar", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTracksMove", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DelayItemLoading", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "#CopilotOverlayVisible", "RuleProviderType": "Unknown"}], "StyleName": "defaultGalleryStyle", "Type": "ControlInfo"}, "GalleryDayHeaderstemplate1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["ItemAccessibleLabel", "TemplateFill", "OnSelect"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "GalleryDayHeaderstemplate1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "ItemAccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateFill", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultGalleryTemplateStyle", "Type": "ControlInfo"}, "IconNextMonth": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Icon", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "Height", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "Set(_firstDay<PERSON><PERSON><PERSON><PERSON><PERSON>, DateAdd(_firstDayOfMonth, 1, Months))", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, "X", "Y", "ZIndex", "Rotation", "AccessibleLabel", "<PERSON><PERSON><PERSON>", "ContentLanguage", "AutoDisableOnSelect", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Visible", "DisplayMode", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight", "PreserveAspectRatio"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "IconNextMonth", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Rotation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoDisableOnSelect", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PreserveAspectRatio", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "defaultIconStyle", "Type": "ControlInfo"}, "IconPreviousMonth": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Icon", "Height", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "Set(_firstDay<PERSON><PERSON><PERSON><PERSON><PERSON>, DateAdd(_firstDayOfMonth, -1, Months))", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, "X", "Y", "ZIndex", "Rotation", "AccessibleLabel", "<PERSON><PERSON><PERSON>", "ContentLanguage", "AutoDisableOnSelect", "Color", "DisabledColor", "PressedColor", "HoverColor", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Visible", "DisplayMode", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "TabIndex", "BorderStyle", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight", "PreserveAspectRatio"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "IconPreviousMonth", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Icon", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Rotation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoDisableOnSelect", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PreserveAspectRatio", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "defaultIconStyle", "Type": "ControlInfo"}, "Label18_1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "Text", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Size", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Label18_1", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "Label3": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Align", "Height", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "VerticalAlign", "X", "Y", "Visible", "DisplayMode", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Label3", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblMonthTitle": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Align", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "Color", "DisabledColor", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblMonthTitle", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "Screen1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "LoadingSpinnerColor", "OnVisible", "Height", "<PERSON><PERSON><PERSON>", "Size", "Orientation", "LoadingSpinner", "ImagePosition"], "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "Screen1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Orientation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ImagePosition", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnVisible", "RuleProviderType": "Unknown"}], "StyleName": "defaultScreenStyle", "Type": "ControlInfo"}}, "TopParentName": "Screen1"}
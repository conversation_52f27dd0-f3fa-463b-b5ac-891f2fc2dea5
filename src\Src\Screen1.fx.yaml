Screen1 As screen:
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)
    OnVisible: =Set(_firstDayOfMonth, Date(Year(Today()), Month(Today()), 1))

    LblMonthTitle As label:
        Align: =Align.Center
        Height: =74
        Size: =24
        Text: =Text(_firstDayOfMonth, "mmmm yyyy")
        Width: =560
        X: =240
        ZIndex: =1

    IconPreviousMonth As icon.ChevronLeft:
        Height: =74
        Icon: =Icon.ChevronLeft
        OnSelect: =Set(_firstDayOfMonth, DateAdd(_firstDayOfMonth, -1, TimeUnit.Months))
        Width: =54
        X: =347
        Y: =6
        ZIndex: =2

    IconNextMonth As icon.ChevronRight:
        Height: =54
        Icon: =Icon.ChevronRight
        OnSelect: =Set(_firstDayOfMonth, DateAdd(_firstDayOfMonth, 1, TimeUnit.Months))
        Width: =54
        X: =643
        Y: =10
        ZIndex: =3

    GalleryDayHeaders As gallery.galleryHorizontal:
        Height: =58
        Items: =["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
        TemplatePadding: =0
        TemplateSize: =Self.Width / 7
        Width: =1058
        X: =10
        Y: =80
        ZIndex: =4

        Label3 As label:
            Align: =Align.Center
            Height: =Parent.TemplateHeight
            OnSelect: =Select(Parent)
            Size: =16
            Text: =ThisItem.Value
            Width: =Parent.TemplateWidth
            ZIndex: =1

    galCalendar_1 As gallery.galleryVertical:
        '#CopilotOverlayLabel': ="Filtered"
        BorderColor: =RGBA(131, 24, 75, 1)
        DelayItemLoading: =true
        Height: =541
        Items: =Sequence(42)
        Layout: =Layout.Vertical
        LoadingSpinner: =LoadingSpinner.Data
        TemplateSize: =96
        Width: =1069
        WrapCount: =7
        X: =60
        Y: =94
        ZIndex: =5

        Label18_1 As label:
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =70
            OnSelect: =Select(Parent)
            Size: =21
            Text: =Day(DateAdd(_firstDayOfMonth, ThisItem.Value - Weekday(_firstDayOfMonth)))
            Width: =147.42857142857142
            Y: =40
            ZIndex: =1


<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/dropdown" name="dropdown" jsClass="AppMagic.Controls.Dropdown.DropdownControl" version="2.3.1" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[DROPDOWN
      Control description here.]]></description>
  <requires>
    <require type="css" src="css/dropdown.css" />
    <require type="javascript" src="js/dropdown.js" />
    <require type="other" src="data/DropdownSample.xlsx" authoringOnly="true" />
    <require type="other" src="images/dropdownarrow_blackicon.svg" />
  </requires>
  <appMagic:capabilities contextualViewsEnabled="true" autoBorders="true" autoFocusedBorders="true" screenActiveAware="true" autoDisabledViewState="true" autoPointerViewState="true" isVersionFlexible="true" />
  <appMagic:accessibilityChecks controlIsInteractive="true" />
  <content><![CDATA[
    <div class="appmagic-dropdown no-focus-outline"
      data-bind="
        shortcut: {
          provider: shortcutProvider,
          capture: false
        }
      ">
      <div
        data-bind="
          style: {
            fontFamily: properties.Font,
            fontSize: properties.Size,
            color: autoProperties.Color,
            backgroundColor: autoProperties.Fill,
            fontWeight: properties.FontWeight,
            fontStyle: properties.Italic,
            textAlign: properties.Align
          },
          attr: {
            class: id + ' appmagic-dropdownLabel',
          },
          event: {
            click: onClickLabel,
          },
          css: {
            disabled: isDisabled,
            underline: properties.Underline,
            readonly: isReadOnly,
            strikethrough: properties.Strikethrough
          },
          controlFlyout: {
            contentSelector: '.appmagic-dropdownFlyout',
            position: 'bottom left',
            openOn: null,
            matchWidth: true,
            beforeOpen: beforeOpenFlyout.bind($data),
            showEvent: openFlyoutEvent,
            closeEvent: closeFlyoutEvent,
            onShow: onShow.bind($data),
            onClose: onClose.bind($data)
          }
        "
      >
        <div class="appmagic-dropdownLabelText no-focus-outline"
          touch-action="pan-x pan-y"
          role="button"
          aria-haspopup="listbox"
          data-bind="
            attr: {
              title: properties.Tooltip() || null,
              'aria-label': (properties.AccessibleLabel() || '') + '. ' + selectedValue,
              'aria-disabled': isDisabled || isReadOnly
            },
            text: selectedValue,
            style: {
              paddingTop: properties.PaddingTop,
              paddingRight: properties.PaddingRight,
              paddingBottom: properties.PaddingBottom,
              paddingLeft: properties.PaddingLeft
            }
          "
        >
        </div>

        <div class="appmagic-dropdownLabelArrow" data-bind="style: { width: properties.ChevronWidth }">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" focusable="false" aria-hidden="true"
            data-bind="style: { width: properties.ChevronWidth, height: AppMagic.Controls.converters.pxVerticalConverter.view(properties.Height()) }">
              <polygon class="chevron-svg" points="18,24.5 7,13.6 9.1,11.5 18,20.3 26.9,11.5 29,13.6" />
          </svg>
        </div>
      </div>
      <!-- ko stopBinding: true -->
      <div
        class="appmagic-dropdownFlyout"
        data-bind="
          shortcut: {
            provider: shortcutProvider,
            capture: false
          },
          attr: {
            id: 'appmagic-dropdownFlyout' + id,
          }
        "
      >
        <div
          role="listbox"
          data-bind="
            style: {
              fontFamily: properties.Font,
              fontSize: properties.Size,
              fontWeight: properties.FontWeight,
              maxHeight: flyoutMaximumHeight,
              fontStyle: properties.Italic,
              textAlign: properties.Align,
              borderWidth: properties.BorderThickness,
              borderColor: properties.HoverBorderColor,
              borderStyle: properties.BorderStyle,
              backgroundColor: properties.Fill,
            },
            attr: {
              class: id + ' appmagic-dropdownList',
              'aria-label': Core.Utility.formatString(AppMagic.Strings.ListBoxItemsLabel || '', properties.AccessibleLabel())
            },
            css: {
              underline: properties.Underline,
              strikethrough: properties.Strikethrough
            },
            foreach: currentItems
          "
        >
          <div
            touch-action="pan-y"
            role="option"
            tabindex="-1"
            data-bind="
              attr: {
                  class: 'item' + $index() + ' appmagic-dropdownListItem',
                  id: 'appmagic-dropdownFlyout' + $parent.id + '_' + $index(),
                  'aria-selected': selected
              },
              style: {
                  minHeight: 'calc(1.333em + ' + $parent.properties.PaddingTop() + ' + ' + $parent.properties.PaddingBottom() + ')',
                  paddingTop: $parent.properties.PaddingTop,
                  paddingRight: $parent.properties.PaddingRight,
                  paddingBottom: $parent.properties.PaddingBottom,
                  paddingLeft: $parent.properties.PaddingLeft},
              css: {
                  selected: selected
              },
              event: {
                  click: $parent.onClickItem.bind($parent),
                  mouseover: onMouseEvent,
                  pointerout: onMouseEvent,
                  mousedown: onMouseEvent,
                  pointerdown: onMouseEvent,
                  pointerup: onMouseEvent
              },
              text: value
            "
          ></div>
          </div>
      </div>
      <!-- /ko -->
    </div>
    <style data-bind="text: dropdownStyles"></style>
    ]]></content>
  <properties>
    <property name="Items" localizedName="##dropdown_Items##" datatype="Array" direction="in" isPrimaryInputProperty="true" hasEditableNameMap="true" errorCapability="recordErrors">
      <title>dropdown items</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##dropdown_Items_DisplayName##</appMagic:displayName>
      <appMagic:sampleDataSource name="DropDownSample" location="data/DropdownSample.xlsx" />
      <properties>
        <property name="Value" localizedName="##dropdown_Value##" datatype="String"></property>
      </properties>
      <appMagic:tooltip>##dropdown_Items_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Selected" localizedName="##dropdown_Selected##" datatype="object" direction="out" supportsAutomation="true">
      <title>Value of the selected item</title>
      <appMagic:passThroughReference>Items</appMagic:passThroughReference>
      <appMagic:category>data</appMagic:category>
    </property>
    <property name="SelectedText" localizedName="##dropdown_SelectedText##" datatype="object" direction="out" isPrimaryOutputProperty="true" supportsAutomation="true">
      <title>Text value of the selected item</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:passThroughReference>Items</appMagic:passThroughReference>
      <properties>
        <property name="Value" localizedName="##dropdown_Value##" datatype="String"></property>
      </properties>
    </property>
    <property name="Default" localizedName="##dropdown_Default##" datatype="String" defaultValue="1" editable="true" direction="in">
      <title>Selected item</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##dropdown_Default_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##dropdown_Default_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Reset" localizedName="##commonProperties_Reset##" datatype="Boolean" defaultValue="false" direction="in">
      <title>Reset</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip>
    </property>
    <property name="AllowEmptySelection" localizedName="##dropdown_AllowEmptySelection##" datatype="Boolean" defaultValue="false" direction="in">
      <title>AllowEmptySelection</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##dropdown_AllowEmptySelection_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##dropdown_AllowEmptySelection_Tooltip##</appMagic:tooltip>
    </property>
    <property name="SelectionColor" localizedName="##dropdown_SelectionColor##" datatype="Color" defaultValue="RGBA(255, 255, 255, 1)" isExpr="true" converter="argbConverter">
      <title>Selected Text color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##dropdown_SelectionColor_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##dropdown_SelectionColor_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ChevronWidth" localizedName="##Dropdown_Chevron_Width##" datatype="Number" defaultValue="34" phoneDefaultValue="50" webDefaultValue="32" converter="pxHorizontalConverter" hidden="true" styleable="true">
      <title>Dropdown Chevron Width</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Dropdown_Chevron_Width_DisplayName##</appMagic:displayName>
    </property>
    <property name="FlyoutMaximumHeight" localizedName="##Dropdown_Flyout_MaximumHeight##" datatype="Number" defaultValue="400" converter="pxVerticalConverter" phoneDefaultValue="700" hidden="true" styleable="true">
      <title>Dropdown Flyout Maximum Height</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##Dropdown_Flyout_MaximumHeight_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronFill" localizedName="##Dropdown_Chevron_Fill##" datatype="Color" isExpr="true" defaultValue="RGBA(255, 255, 255, 1)" converter="argbConverter">
      <title>Dropdown Chevron Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Dropdown_Chevron_Fill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##Dropdown_Chevron_Fill_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ChevronHoverFill" localizedName="##Dropdown_Chevron_HoverFill##" datatype="Color" isExpr="true" defaultValue="Self.ChevronFill" converter="argbConverter" styleable="true">
      <title>Dropdown Chevron Hover Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Dropdown_Chevron_HoverFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronDisabledFill" localizedName="##Dropdown_Chevron_DisabledFill##" datatype="Color" isExpr="true" defaultValue="RGBA(235, 233, 229, 1)" converter="argbConverter" styleable="true">
      <title>Dropdown Chevron Disabled Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Dropdown_Chevron_DisabledFill_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronBackground" localizedName="##Dropdown_Chevron_Background##" datatype="Color" isExpr="true" defaultValue="RGBA(35, 31, 32, 1)" converter="argbConverter">
      <title>Dropdown Chevron Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Dropdown_Chevron_Background_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##Dropdown_Chevron_Background_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ChevronHoverBackground" localizedName="##Dropdown_Chevron_HoverBackground##" datatype="Color" isExpr="true" defaultValue="ColorFade(Self.ChevronBackground, 20%)" converter="argbConverter" styleable="true">
      <title>Dropdown Chevron Hover Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Dropdown_Chevron_HoverBackground_DisplayName##</appMagic:displayName>
    </property>
    <property name="ChevronDisabledBackground" localizedName="##Dropdown_Chevron_DisabledBackground##" datatype="Color" isExpr="true" defaultValue="RGBA(215, 210, 204, 1)" converter="argbConverter" styleable="true">
      <title>Dropdown Chevron Disabled Fill</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##Dropdown_Chevron_DisabledBackground_DisplayName##</appMagic:displayName>
    </property>
    <property name="SelectionFill" localizedName="##dropdown_SelectionFill##" datatype="Color" defaultValue="RGBA(0, 176, 240, 1)" isExpr="true" converter="argbConverter">
      <title>Selected Fill color</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##dropdown_SelectionFill_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##dropdown_SelectionFill_Tooltip##</appMagic:tooltip>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Data -->
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="Tooltip" />
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="Color" defaultValue="RGBA(0, 0, 0, 1)" isExpr="true" />
    <appMagic:includeProperty name="HoverColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="PressedColor" defaultValue="Self.Color" />
    <appMagic:includeProperty name="DisabledColor" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="BorderColor" defaultValue="RGBA(69, 69, 69, 1)" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="ColorFade(Self.BorderColor, 15%)" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="Self.HoverBorderColor" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" defaultValue="2" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="4" />
    <appMagic:includeProperty name="Fill" defaultValue="RGBA(255, 255, 255, 1)" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="RGBA(242, 242, 242, 1)" />
    <appMagic:includeProperty name="PressedFill" defaultValue="ColorFade(Self.SelectionFill, -30%)" />
    <appMagic:includeProperty name="HoverFill" defaultValue="RGBA(186, 186, 186, 1)" />
    <appMagic:includeProperty name="Font" />
    <appMagic:includeProperty name="Size" phoneDefaultValue="30" />
    <appMagic:includeProperty name="FontWeight" />
    <appMagic:includeProperty name="Italic" />
    <appMagic:includeProperty name="Underline" />
    <appMagic:includeProperty name="Strikethrough" />
    <appMagic:includeProperty name="PaddingTop" defaultValue="10" />
    <appMagic:includeProperty name="PaddingRight" defaultValue="10" />
    <appMagic:includeProperty name="PaddingBottom" defaultValue="10" />
    <appMagic:includeProperty name="PaddingLeft" defaultValue="10" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="328" phoneDefaultValue="575" webDefaultValue="180" />
    <appMagic:includeProperty name="Height" defaultValue="40" phoneDefaultValue="70" webDefaultValue="32" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="TabIndex" />
    <appMagic:includeProperty name="DisplayMode" />
    <!-- Behavior -->
    <appMagic:includeProperty name="OnSelect" direction="in" />
    <appMagic:includeProperty name="OnChange" direction="in" isPrimaryBehaviorProperty="true" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="80" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="35" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
  </appMagic:includeProperties>
  <!--Property Dependencies -->
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="Items" output="Selected" />
    <appMagic:propertyDependency input="Default" output="Selected" />
    <appMagic:propertyDependency input="Reset" output="Selected" />
    <appMagic:propertyDependency input="Items" output="SelectedText" />
    <appMagic:propertyDependency input="Default" output="SelectedText" />
  </appMagic:propertyDependencies>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Input" priority="40" />
    <appMagic:category name="ClassicControls" priority="40" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Items" serverProvidesValue="true" hasNameMaps="true" computedValueType="ItemsWithNameMaps" />
      <appMagic:configureProperty propertyInvariantName="DependentItems" ruleToSet="Items" hasNameMaps="true" computedValueType="ItemsWithNameMaps" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Default" />
      <appMagic:property name="DisplayMode" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Padding">
        <appMagic:property name="PaddingTop" labelOverride="##Padding_Top_Title##" />
        <appMagic:property name="PaddingBottom" labelOverride="##Padding_Bottom_Title##" />
        <appMagic:property name="PaddingLeft" labelOverride="##Padding_Left_Title##" />
        <appMagic:property name="PaddingRight" labelOverride="##Padding_Right_Title##" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Color" showInFloatie="true" />
        <appMagic:property name="Fill" showInFloatie="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="SelectionColor">
        <appMagic:property name="SelectionColor" />
        <appMagic:property name="SelectionFill" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="ChevronColor">
        <appMagic:property name="ChevronFill" />
        <appMagic:property name="ChevronBackground" />
      </appMagic:propertyGroup>
      <appMagic:property name="Font" displayType="FontEnum" showInFloatie="true" />
      <appMagic:property name="Size" labelOverride="##FontSize_Property##" showInFloatie="true" />
      <appMagic:property name="FontWeight" displayType="EnumIcon" itemsOrder="Bold;Semibold;Normal;Lighter" />
      <appMagic:propertyGroup name="Style">
        <appMagic:property name="Italic" displayType="ToggleButton" />
        <appMagic:property name="Underline" displayType="ToggleButton" />
        <appMagic:property name="Strikethrough" displayType="ToggleButton" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="DisabledColor">
        <appMagic:property name="DisabledColor" />
        <appMagic:property name="DisabledFill" />
        <appMagic:property name="DisabledBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="ChevronDisabledFill" />
      <appMagic:property name="ChevronDisabledBackground" />
      <appMagic:propertyGroup name="HoverColor">
        <appMagic:property name="HoverColor" />
        <appMagic:property name="HoverFill" />
        <appMagic:property name="HoverBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="ChevronHoverFill" />
      <appMagic:property name="ChevronHoverBackground" />
      <appMagic:propertyGroup name="PressedColor">
        <appMagic:property name="PressedColor" />
        <appMagic:property name="PressedFill" />
        <appMagic:property name="PressedBorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="TabIndex" />
      <appMagic:property name="Tooltip" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.1.0">
    <appMagic:conversionAction type="add" name="AllowUnsetValue" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <appMagic:conversionAction type="rename" name="Selected" newName="SelectedText" />
    <appMagic:conversionAction type="add" name="Selected" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.2.1">
    <appMagic:conversionAction type="rename" name="AllowUnsetValue" newName="AllowEmptySelection" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.1" to="2.2.2">
    <!-- KO template changes for accessibility fixes -->
  </appMagic:conversion>
  <appMagic:conversion from="2.2.2" to="2.2.3">
    <!-- KO template changes for undefined string fix -->
  </appMagic:conversion>
  <appMagic:conversion from="2.2.3" to="2.2.4">
    <!-- Defined Items property as able to receive record errors -->
  </appMagic:conversion>
  <appMagic:conversion from="2.2.4" to="2.3.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.3.1">
    <!-- KO template changes for fixing min height issue -->
  </appMagic:conversion>
</widget>
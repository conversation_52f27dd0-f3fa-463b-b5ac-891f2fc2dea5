{"$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.4.json", "runs": [{"columnKind": "utf16CodeUnits", "invocations": [{"executionSuccessful": true}], "results": [{"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconRefresh1.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconRefresh1.OnSelect", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 10, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconRefresh1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconRefresh1.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconRefresh1.OnSelect", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 0, "snippet": {"text": "Refresh"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconRefresh1"}}], "message": {"arguments": ["Refresh"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconNewItem1.DisplayMode"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconNewItem1.DisplayMode", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 20, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayMode", "module": "FirstPage", "type": "FirstPage.IconNewItem1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconNewItem1.DisplayMode"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconNewItem1.DisplayMode", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 3, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayMode", "module": "FirstPage", "type": "FirstPage.IconNewItem1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.DataSource"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.DataSource", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 0, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DataSource", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Name_DataCard2"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Name_DataCard2"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Name)"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Name_DataCard2"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.Default", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Name"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Name_DataCard2"}}], "message": {"arguments": ["Name"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 55, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 68, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave From')"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'.Default", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave From'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave From_DataCard1'"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 55, "snippet": {"text": "'Leave To'"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 66, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave To')"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 8, "snippet": {"text": ".'Leave To'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave To_DataCard1'"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Location_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Location_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Location)"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Location_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Location_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": ".Location"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Location_DataCard1"}}], "message": {"arguments": ["Location"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 55, "snippet": {"text": "Approval"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Approval)"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": ".<PERSON><PERSON><PERSON><PERSON>"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Role_DataCard2"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Role_DataCard2"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 4, "charOffset": 55, "snippet": {"text": "Role"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Role_DataCard2"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Role)"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Role_DataCard2"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Role_DataCard2.Default", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Role"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Role_DataCard2"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 55, "snippet": {"text": "'Leave Type'"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'"}}], "message": {"arguments": ["Leave Type"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 68, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave Type')"}}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.Default", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'"}}], "message": {"arguments": ["Leave Type"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.Items", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 10, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.Items", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 20, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.Items", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 0, "snippet": {"text": "Choices"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14"}}], "message": {"arguments": ["Choices"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 3, "charOffset": 208, "snippet": {"text": ".ID"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"arguments": ["ID"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 3, "charOffset": 228, "snippet": {"text": ".ID"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"arguments": ["ID"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 212, "snippet": {"text": "="}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"arguments": ["Error", "Error"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrIncompatibleTypesForEquality-Left-Right", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 289, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 17, "charOffset": 300, "snippet": {"text": "gblRecordToDelete"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-ErrCollectionDoesNotAcceptThisType", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 281, "snippet": {"text": "Remove"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"arguments": ["Remove"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 423, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.OnSelect", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 414, "snippet": {"text": "Refresh"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"arguments": ["Refresh"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 39, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 20, "snippet": {"text": "Filter"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 406, "snippet": {"text": "Approval"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 415, "snippet": {"text": "="}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Error", "Boolean"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrIncompatibleTypesForEquality-Left-Right", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 499, "snippet": {"text": "Approval"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 508, "snippet": {"text": "="}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Error", "Boolean"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrIncompatibleTypesForEquality-Left-Right", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 848, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 927, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 1003, "snippet": {"text": "\"LeaveFrom\""}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["LeaveFrom"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrColDNE-Name", "ruleIndex": 5}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 0, "snippet": {"text": "SortByColumns"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["SortByColumns"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Title1.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Title1.Text", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Name"}}}, "properties": {"member": "Text", "module": "FirstPage", "type": "FirstPage.BrowseGallery1.Title1"}}], "message": {"arguments": ["Name"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.LeaveFrom.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.LeaveFrom.Text", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 8, "snippet": {"text": ".Title"}}}, "properties": {"member": "Text", "module": "FirstPage", "type": "FirstPage.BrowseGallery1.LeaveFrom"}}], "message": {"arguments": ["Title"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.LeaveTo.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.LeaveTo.Text", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave From'"}}}, "properties": {"member": "Text", "module": "FirstPage", "type": "FirstPage.BrowseGallery1.LeaveTo"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.DataSource"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.DataSource", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 0, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DataSource", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Name)"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.Default", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Name"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"arguments": ["Name"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Name)"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 55, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 68, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave From')"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.Default", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave From'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 55, "snippet": {"text": "'Leave To'"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 66, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave To')"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 8, "snippet": {"text": ".'Leave To'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Location)"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.MaxLength", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.MaxLength", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.MaxLength", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Location)"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 55, "snippet": {"text": "Approval"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Approval)"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": ".<PERSON><PERSON><PERSON><PERSON>"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 55, "snippet": {"text": "'Leave Type'"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'"}}], "message": {"arguments": ["Leave Type"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 68, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave Type')"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.Default", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'"}}], "message": {"arguments": ["Leave Type"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.Items", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 10, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "Items", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.Items", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 20, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "Items", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.Items", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 0, "snippet": {"text": "Choices"}}}, "properties": {"member": "Items", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22"}}], "message": {"arguments": ["Choices"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 55, "snippet": {"text": "Scheduled"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1"}}], "message": {"arguments": ["Scheduled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 65, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Scheduled)"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 8, "snippet": {"text": ".Scheduled"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1"}}], "message": {"arguments": ["Scheduled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 16, "charOffset": 55, "snippet": {"text": "'Requested Time'"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'"}}], "message": {"arguments": ["Requested Time"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 72, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Requested Time')"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.Default", "relativeAddress": 0}, "region": {"charLength": 17, "charOffset": 8, "snippet": {"text": ".'Requested Time'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'"}}], "message": {"arguments": ["Requested Time"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 55, "snippet": {"text": "CreatedBY"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'"}}], "message": {"arguments": ["CreatedBY"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 65, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,CreatedBY)"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.Default", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Created BY'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'"}}], "message": {"arguments": ["Created BY"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.SearchItems", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "SearchItems", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.SearchItems", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 17, "snippet": {"text": ".CreatedBY"}}}, "properties": {"member": "SearchItems", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.SearchItems", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 0, "snippet": {"text": "Choices"}}}, "properties": {"member": "SearchItems", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"arguments": ["Choices"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.Items", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 10, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "Items", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.Items", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 20, "snippet": {"text": ".CreatedBY"}}}, "properties": {"member": "Items", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.Items", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 0, "snippet": {"text": "Choices"}}}, "properties": {"member": "Items", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"arguments": ["Choices"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 55, "snippet": {"text": "canceled"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1"}}], "message": {"arguments": ["canceled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,canceled)"}}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": ".canceled"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1"}}], "message": {"arguments": ["canceled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 25, "charOffset": 19, "snippet": {"text": "'Site Staff Combiner Indiana'"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Site Staff Combiner Indiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 7, "snippet": {"text": "ShowColumns"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["ShowColumns"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 45, "snippet": {"text": "Title"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Title"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 66, "snippet": {"text": "Manager"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Manager"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 74, "snippet": {"text": "Supervisor"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Supervisor"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 85, "snippet": {"text": "'Shift Start'"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Shift Start"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 99, "snippet": {"text": "'Shift End'"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Shift End"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 104, "charOffset": 7, "snippet": {"text": "ShowColumns('Site Staff Combiner Indiana',Title,Classification,Manager,Supervisor,'Shift Start','Shift End')"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrSearchWrongTableType", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 133, "snippet": {"text": "Title"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Title"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrColDNE-Name", "ruleIndex": 5}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 0, "snippet": {"text": "Search"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Search"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}, "region": {"charLength": 25, "charOffset": 12, "snippet": {"text": "'Site Staff Combiner Indiana'"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Site Staff Combiner Indiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 0, "snippet": {"text": "ShowColumns"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["ShowColumns"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 38, "snippet": {"text": "Title"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Title"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 59, "snippet": {"text": "Manager"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Manager"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 67, "snippet": {"text": "Supervisor"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Supervisor"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 78, "snippet": {"text": "'Shift Start'"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Shift Start"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 92, "snippet": {"text": "'Shift End'"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"arguments": ["Shift End"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftStart.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftStart.Text", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 61, "snippet": {"text": ".ShiftStart"}}}, "properties": {"member": "Text", "module": "EnterData", "type": "EnterData.ShiftStart"}}], "message": {"arguments": ["ShiftStart"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftStart.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftStart.Text", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 0, "snippet": {"text": "If"}}}, "properties": {"member": "Text", "module": "EnterData", "type": "EnterData.ShiftStart"}}], "message": {"arguments": ["If"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftEnd.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftEnd.Text", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 61, "snippet": {"text": ".ShiftEnd"}}}, "properties": {"member": "Text", "module": "EnterData", "type": "EnterData.ShiftEnd"}}], "message": {"arguments": ["ShiftEnd"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftEnd.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftEnd.Text", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 0, "snippet": {"text": "If"}}}, "properties": {"member": "Text", "module": "EnterData", "type": "EnterData.ShiftEnd"}}], "message": {"arguments": ["If"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Item"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Item", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 51, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Item"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Item", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 41, "snippet": {"text": "De<PERSON>ults"}}}, "properties": {"member": "<PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1"}}], "message": {"arguments": ["De<PERSON>ults"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.DataSource"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.DataSource", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 2, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DataSource", "module": "EnterData", "type": "EnterData.EditForm1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 55, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 68, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave From')"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.Default", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave From'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 36, "snippet": {"text": ".ShiftStart"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1"}}], "message": {"arguments": ["ShiftStart"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.Default", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 10, "snippet": {"text": "IsBlank"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1"}}], "message": {"arguments": ["IsBlank"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 141, "snippet": {"text": ".ShiftStart"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1"}}], "message": {"arguments": ["ShiftStart"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 36, "snippet": {"text": ".ShiftStart"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1"}}], "message": {"arguments": ["ShiftStart"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.Default", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 10, "snippet": {"text": "IsBlank"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1"}}], "message": {"arguments": ["IsBlank"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 143, "snippet": {"text": ".ShiftStart"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1"}}], "message": {"arguments": ["ShiftStart"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 55, "snippet": {"text": "'Leave To'"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 66, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave To')"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 8, "snippet": {"text": ".'Leave To'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 36, "snippet": {"text": ".ShiftEnd"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2"}}], "message": {"arguments": ["ShiftEnd"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.Default", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 10, "snippet": {"text": "IsBlank"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2"}}], "message": {"arguments": ["IsBlank"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 139, "snippet": {"text": ".ShiftEnd"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2"}}], "message": {"arguments": ["ShiftEnd"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 36, "snippet": {"text": ".ShiftEnd"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2"}}], "message": {"arguments": ["ShiftEnd"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.Default", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 10, "snippet": {"text": "IsBlank"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2"}}], "message": {"arguments": ["IsBlank"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 141, "snippet": {"text": ".ShiftEnd"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2"}}], "message": {"arguments": ["ShiftEnd"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 55, "snippet": {"text": "Approval"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Approval)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": ".<PERSON><PERSON><PERSON><PERSON>"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2"}}], "message": {"arguments": ["Approval"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 55, "snippet": {"text": "Scheduled"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2"}}], "message": {"arguments": ["Scheduled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 65, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Scheduled)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.Default", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 8, "snippet": {"text": ".Scheduled"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2"}}], "message": {"arguments": ["Scheduled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 55, "snippet": {"text": "'Leave Type'"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown"}}], "message": {"arguments": ["Leave Type"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.DisplayName", "relativeAddress": 0}, "region": {"charLength": 68, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave Type')"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.Default", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown"}}], "message": {"arguments": ["Leave Type"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.Items", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 10, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.Items", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 20, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.Items", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 0, "snippet": {"text": "Choices"}}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"arguments": ["Choices"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.SearchItems", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.SearchItems", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 17, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.SearchItems", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 0, "snippet": {"text": "Choices"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"arguments": ["Choices"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 55, "snippet": {"text": "canceled"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2"}}], "message": {"arguments": ["canceled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,canceled)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 8, "snippet": {"text": ".canceled"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2"}}], "message": {"arguments": ["canceled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.Default", "relativeAddress": 0}, "region": {"charLength": 17, "charOffset": 8, "snippet": {"text": ".'Requested Time'"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'"}}], "message": {"arguments": ["Requested Time"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DisplayMode"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DisplayMode", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 34, "snippet": {"text": ".Value"}}}, "properties": {"member": "DisplayMode", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'"}}], "message": {"arguments": ["Value"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DisplayMode"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DisplayMode", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 42, "snippet": {"text": "="}}}, "properties": {"member": "DisplayMode", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'"}}], "message": {"arguments": ["Error", "Text"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrIncompatibleTypesForEquality-Left-Right", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 4, "charOffset": 55, "snippet": {"text": "Role"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Role)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 4, "charOffset": 55, "snippet": {"text": "Role"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Role)"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Role"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.Default", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 18, "snippet": {"text": ".field_6"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1.DataCardValue12"}}], "message": {"arguments": ["field_6"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DisplayName", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Location)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.MaxLength", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.MaxLength", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.MaxLength", "relativeAddress": 0}, "region": {"charLength": 64, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Location)"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 55, "snippet": {"text": "'Created BY'"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'"}}], "message": {"arguments": ["Created BY"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DisplayName", "relativeAddress": 0}, "region": {"charLength": 68, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Created BY')"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 7, "snippet": {"text": "User()"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 0, "snippet": {"text": "Search"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"arguments": ["Search"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 40, "snippet": {"text": "Email"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"arguments": ["Email"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 55, "snippet": {"text": "Supervisor"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["Supervisor"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 66, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Supervisor)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 55, "snippet": {"text": "Supervisor"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["Supervisor"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 66, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Supervisor)"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 8, "snippet": {"text": ".Supervisor"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1"}}], "message": {"arguments": ["Supervisor"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 18, "snippet": {"text": ".field_12"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16"}}], "message": {"arguments": ["field_12"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 55, "snippet": {"text": "Manager"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["Manager"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 63, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.Di<PERSON><PERSON>N<PERSON>,Manager)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 55, "snippet": {"text": "Manager"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["Manager"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 63, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana], DataSourceInfo<PERSON>, Manager)"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 8, "snippet": {"text": ".Manager"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1"}}], "message": {"arguments": ["Manager"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.Default", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 18, "snippet": {"text": ".field_13"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15"}}], "message": {"arguments": ["field_13"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.Default", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Name"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["Name"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Name)"}}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.Update"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.Update", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 18, "snippet": {"text": ".Title"}}}, "properties": {"member": "Update", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["Title"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 17, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 14, "charOffset": 0, "snippet": {"text": "DataSourceInfo"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["DataSourceInfo"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength", "relativeAddress": 0}, "region": {"charLength": 60, "charOffset": 0, "snippet": {"text": "DataSourceInfo([@TimeOffIndiana], DataSourceInfo.MaxLength, Name)"}}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"arguments": ["Unknown"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType-Type", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.Default"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.Default", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 18, "snippet": {"text": ".Title"}}}, "properties": {"member": "<PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1.DataCardValue10"}}], "message": {"arguments": ["Title"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.Items", "relativeAddress": 0}, "region": {"charLength": 19, "charOffset": 672, "snippet": {"text": "DateTimeValue(Date)"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"arguments": ["DateTimeValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 692, "snippet": {"text": ">="}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 694, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 675, "snippet": {"text": "Filter"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 730, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 781, "snippet": {"text": "'Leave To'"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 878, "snippet": {"text": "canceled"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["canceled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 887, "snippet": {"text": "="}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Error", "Boolean"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrIncompatibleTypesForEquality-Left-Right", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 908, "snippet": {"text": "canceled"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["canceled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 917, "snippet": {"text": "="}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Error", "Boolean"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrIncompatibleTypesForEquality-Left-Right", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 936, "snippet": {"text": "canceled"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["canceled"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 928, "snippet": {"text": "IsBlank"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["IsBlank"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 4, "charOffset": 1225, "snippet": {"text": "Role"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 4, "charOffset": 1184, "snippet": {"text": "Sort"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Sort"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 16, "charOffset": 1444, "snippet": {"text": "'Requested Time'"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Requested Time"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Name.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Name.Text", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Name"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences.Name"}}], "message": {"arguments": ["Name"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.RequestedTime.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.RequestedTime.Text", "relativeAddress": 0}, "region": {"charLength": 17, "charOffset": 8, "snippet": {"text": ".'Requested Time'"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences.RequestedTime"}}], "message": {"arguments": ["Requested Time"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Leave_to.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Leave_to.Text", "relativeAddress": 0}, "region": {"charLength": 11, "charOffset": 8, "snippet": {"text": ".'Leave To'"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences.Leave_to"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Leave_from.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Leave_from.Text", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave From'"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences.Leave_from"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.LeaveType.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.LeaveType.Text", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 8, "snippet": {"text": ".'Leave Type'"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences.LeaveType"}}], "message": {"arguments": ["Leave Type"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.LeaveType.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.LeaveType.Text", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 21, "snippet": {"text": ".Value"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences.LeaveType"}}], "message": {"arguments": ["Error"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidDot", "ruleIndex": 9}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Classification.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Classification.Text", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Role"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences.Classification"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 9, "charOffset": 623, "snippet": {"text": "TimeOffIndiana"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["TimeOffIndiana"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 606, "snippet": {"text": "Filter"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 721, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 10, "charOffset": 768, "snippet": {"text": "'Leave To'"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["Leave To"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 4, "charOffset": 810, "snippet": {"text": "Role"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrColDNE-Name", "ruleIndex": 5}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 590, "snippet": {"text": "GroupBy"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["GroupBy"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Title12.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Title12.Text", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 8, "snippet": {"text": ".Role"}}}, "properties": {"member": "Text", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut.Title12"}}], "message": {"arguments": ["Role"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconEdit.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconEdit.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.IconEdit"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconEdit.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconEdit.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.IconEdit"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.DatePicker_StartFirstPage.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.DatePicker_StartFirstPage.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.DatePicker_StartFirstPage"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.DatePicker_EndFirstPage.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.DatePicker_EndFirstPage.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.DatePicker_EndFirstPage"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.ddApprovalFilter.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.ddApprovalFilter.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.ddApprovalFilter"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftStartText.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftStartText.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ShiftStartText"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftEndText.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftEndText.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ShiftEndText"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1.DataCardValue12"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DataCardValue11.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DataCardValue11.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3.DataCardValue11"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1.DataCardValue10"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.TextInput2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.TextInput2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.TextInput2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.TextInput3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.TextInput3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.TextInput3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.DatePicker_Start.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.DatePicker_Start.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.DatePicker_Start"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_HourStart.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_HourStart.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_HourStart"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_MinStart.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_MinStart.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_MinStart"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.tglShowCanceled.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.tglShowCanceled.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.tglShowCanceled"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_HourEnd.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_HourEnd.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_HourEnd"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.DatePicker_End.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.DatePicker_End.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.DatePicker_End"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_MinEnd.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_MinEnd.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_MinEnd"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortClassification.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortClassification.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortClassification"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortClassification.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortClassification.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortClassification"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortRequestTime"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortRequestTime"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.Gallery1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.Gallery1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "DateRestriction", "type": "DateRestriction.Gallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 10}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.Gallery1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.Gallery1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateRestriction", "type": "DateRestriction.Gallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 11}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1", "relativeAddress": 0}}, "properties": {"module": "Screen1", "type": "Screen1"}}], "message": {"id": "issue"}, "properties": {"level": "Low"}, "ruleId": "acc-ReadableScreenNameNeeded", "ruleIndex": 12}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName", "relativeAddress": 0}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Name_DataCard2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 13}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName", "relativeAddress": 0}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 13}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength", "relativeAddress": 0}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 13}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 13}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 13}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName", "relativeAddress": 0}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 13}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength", "relativeAddress": 0}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 13}], "tool": {"driver": {"fullName": "PowerApps app checker", "name": "PowerApps app checker", "rules": [{"id": "app-ErrInvalidName", "messageStrings": {"issue": {"text": "Name isn't valid. '{0}' isn't recognized."}}, "properties": {"componentType": "app", "howToFix": ["Remove or correct the reference to the name that isn't valid."], "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrInvalidArgs-Func", "messageStrings": {"issue": {"text": "The function '{0}' has some invalid arguments."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrBadType-Type", "messageStrings": {"issue": {"text": "Invalid argument type. Cannot use {0} values in this context."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrIncompatibleTypesForEquality-Left-Right", "messageStrings": {"issue": {"text": "Incompatible types for comparison. These types can't be compared: {0}, {1}."}}, "properties": {"componentType": "app", "howToFix": ["You might need to convert the value to be the same type, such as converting a date string (e.g., \"12/31/2018\") to a date value.", "If you’re comparing records or tables, the field or column types must match exactly."], "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrCollectionDoesNotAcceptThisType", "messageStrings": {"issue": {"text": "Incompatible type. The collection can't contain values of this type."}}, "properties": {"componentType": "app", "howToFix": ["You might need to convert the type of the item using, for example, a Datevalue, Text, or Value function."], "level": "Medium", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrColDNE-Name", "messageStrings": {"issue": {"text": "The specified column '{0}' does not exist."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrSearchWrongTableType", "messageStrings": {"issue": {"text": "Table doesn't contain any column of text type."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrBadType", "messageStrings": {"issue": {"text": "Invalid argument type."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-SuggestRemoteExecutionHint", "messageStrings": {"issue": {"text": "Delegation warning. The \"{0}\" part of this formula might not work correctly on large data sets."}}, "properties": {"componentType": "app", "howToFix": ["If your data set exceeds the 500 record limit but contains less than 2,000 records, try resetting the limit.", "Try simplifying the formula.", "Try moving your data to a different data source."], "level": "Medium", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrInvalidDot", "messageStrings": {"issue": {"text": "The '.' operator cannot be used on {0} values."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "acc-AccessibleLabelNeeded", "messageStrings": {"issue": {"text": "Missing accessible label"}}, "properties": {"componentType": "app", "howToFix": ["Edit the accessible label property to describe the item."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "If there's no accessible text, people who can’t see the screen won't understand what’s in images and controls."}}, {"id": "acc-TabIndexShouldBeDefinedForInteractiveControl", "messageStrings": {"issue": {"text": "Missing tab stop"}}, "properties": {"componentType": "app", "howToFix": ["Set TabIndex to 0 or greater to ensure that interactive elements have a tab stop."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "People who use the keyboard with your app will not be able to access this element without a tab stop."}}, {"id": "acc-ReadableScreenNameNeeded", "messageStrings": {"issue": {"text": "Revise screen name"}}, "properties": {"componentType": "app", "howToFix": ["Give the screen  a title that describes what's on the screen or what it's used for."], "level": "Low", "primaryCategory": "accessibility", "whyFix": "People who are blind, have low vision, or a reading disability rely on screen titles to navigate using the screen reader. "}}, {"id": "app-InefficientDelayLoading", "messageStrings": {"issue": {"text": "Inefficient delay loading"}}, "properties": {"componentType": "app", "howToFix": ["Use variables, collections, and navigation context to share state across screens instead."], "level": "Medium", "primaryCategory": "performance", "whyFix": "Controls that reference controls on other screens can slow down app loading and navigation. Doing this may force the app to load the other screens immediately, rather than waiting until the user navigates to that screen."}}], "version": "1.346"}}}], "version": "2.1.0"}
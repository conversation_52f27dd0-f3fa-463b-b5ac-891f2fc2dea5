{"$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.4.json", "runs": [{"columnKind": "utf16CodeUnits", "invocations": [{"executionSuccessful": true}], "results": [{"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.Label18_1.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.Label18_1.OnSelect", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 117, "snippet": {"text": "Screen2"}}}, "properties": {"member": "OnSelect", "module": "Screen1", "type": "Screen1.galCalendar_1.Label18_1"}}], "message": {"arguments": ["Screen2"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.LblRestrictionInfo.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.LblRestrictionInfo.Text", "relativeAddress": 0}, "region": {"charLength": 15, "charOffset": 184, "snippet": {"text": "DateValue(Date)"}}}, "properties": {"member": "Text", "module": "Screen1", "type": "Screen1.galCalendar_1.LblRestrictionInfo"}}], "message": {"arguments": ["DateValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.LblRestrictionInfo.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.LblRestrictionInfo.Text", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 200, "snippet": {"text": "="}}}, "properties": {"member": "Text", "module": "Screen1", "type": "Screen1.galCalendar_1.LblRestrictionInfo"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.LblAbsenteeCount.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.LblAbsenteeCount.Text", "relativeAddress": 0}, "region": {"charLength": 23, "charOffset": 243, "snippet": {"text": "DateValue('Leave From')"}}}, "properties": {"member": "Text", "module": "Screen1", "type": "Screen1.galCalendar_1.LblAbsenteeCount"}}], "message": {"arguments": ["DateValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.LblAbsenteeCount.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.LblAbsenteeCount.Text", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 267, "snippet": {"text": "<="}}}, "properties": {"member": "Text", "module": "Screen1", "type": "Screen1.galCalendar_1.LblAbsenteeCount"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.LblAbsenteeCount.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.LblAbsenteeCount.Text", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 282, "snippet": {"text": "&&"}}}, "properties": {"member": "Text", "module": "Screen1", "type": "Screen1.galCalendar_1.LblAbsenteeCount"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 7, "snippet": {"text": "User()"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 0, "snippet": {"text": "Search"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"arguments": ["Search"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 40, "snippet": {"text": "Email"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"arguments": ["Email"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.Items", "relativeAddress": 0}, "region": {"charLength": 19, "charOffset": 643, "snippet": {"text": "DateTimeValue(Date)"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"arguments": ["DateTimeValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 663, "snippet": {"text": ">="}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 27, "charOffset": 688, "snippet": {"text": "DateTimeValue('Leave From')"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["DateTimeValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 716, "snippet": {"text": "<="}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 725, "snippet": {"text": "&&"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 775, "snippet": {"text": "&&"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 27, "charOffset": 680, "snippet": {"text": "DateTimeValue('Leave From')"}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["DateTimeValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 708, "snippet": {"text": "<="}}}, "properties": {"member": "Items", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 520, "charOffset": 297, "snippet": {"text": "Switch(\n            ddApprovalFilter.Selected.Value, // Check dropdown selection\n            \"Approved\", Approval = true,  // If \"Approved\", filter where the column is true\n            \"Denied\",   Approval = false, // If \"Denied\", filter where the column is false (or use Not(YourApprovalColumnName))\n            \"All\",      true,                         // If \"All\", this condition is always true (no filtering on approval)\n            true                                  // Default case (also no filtering)\n        )"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Switch"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 826, "snippet": {"text": "&&"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 902, "snippet": {"text": "&&"}}}, "properties": {"member": "Items", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1", "relativeAddress": 0}}, "properties": {"module": "Screen1", "type": "Screen1"}}], "message": {"id": "issue"}, "properties": {"level": "Low"}, "ruleId": "acc-ReadableScreenNameNeeded", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.LblMonthTitle.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.LblMonthTitle.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "Screen1", "type": "Screen1.LblMonthTitle"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 5}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.LblMonthTitle.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.LblMonthTitle.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "Screen1", "type": "Screen1.LblMonthTitle"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.IconPreviousMonth.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.IconPreviousMonth.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "Screen1", "type": "Screen1.IconPreviousMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.IconPreviousMonth.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.IconPreviousMonth.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "Screen1", "type": "Screen1.IconPreviousMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.IconNextMonth.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.IconNextMonth.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "Screen1", "type": "Screen1.IconNextMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.IconNextMonth.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.IconNextMonth.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "Screen1", "type": "Screen1.IconNextMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.GalleryDayHeaders.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.GalleryDayHeaders.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "Screen1", "type": "Screen1.GalleryDayHeaders"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.GalleryDayHeaders.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.GalleryDayHeaders.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "Screen1", "type": "Screen1.GalleryDayHeaders"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "Screen1", "type": "Screen1.galCalendar_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "Screen1.galCalendar_1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "Screen1.galCalendar_1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "Screen1", "type": "Screen1.galCalendar_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftStartText.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftStartText.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ShiftStartText"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftEndText.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftEndText.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ShiftEndText"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1.DataCardValue12"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DataCardValue11.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DataCardValue11.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3.DataCardValue11"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1.DataCardValue10"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.TextInput2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.TextInput2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.TextInput2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.TextInput3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.TextInput3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.TextInput3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.DatePicker_Start.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.DatePicker_Start.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.DatePicker_Start"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_HourStart.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_HourStart.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_HourStart"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_MinStart.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_MinStart.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_MinStart"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Calender.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Calender.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.<PERSON><PERSON>"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.tglShowCanceled.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.tglShowCanceled.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.tglShowCanceled"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_HourEnd.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_HourEnd.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_HourEnd"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.DatePicker_End.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.DatePicker_End.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.DatePicker_End"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Dropdown_MinEnd.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Dropdown_MinEnd.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Dropdown_MinEnd"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortClassification.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortClassification.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortClassification"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortClassification.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortClassification.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortClassification"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortRequestTime"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.icoSortRequestTime.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.icoSortRequestTime"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.Gal_Absences.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.Gal_Absences.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.Gal_Absences"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "AbsencesByDate.galRolesOut.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "AbsencesByDate.galRolesOut.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "AbsencesByDate", "type": "AbsencesByDate.galRolesOut"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconEdit.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconEdit.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.IconEdit"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconEdit.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconEdit.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.IconEdit"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.DatePicker_StartFirstPage.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.DatePicker_StartFirstPage.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.DatePicker_StartFirstPage"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.DatePicker_EndFirstPage.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.DatePicker_EndFirstPage.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.DatePicker_EndFirstPage"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.ddApprovalFilter.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.ddApprovalFilter.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.ddApprovalFilter"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.Gallery1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.Gallery1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "DateRestriction", "type": "DateRestriction.Gallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.Gallery1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.Gallery1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateRestriction", "type": "DateRestriction.Gallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "App.varSelectedDate"}], "physicalLocation": {"address": {"fullyQualifiedName": "App.varSelectedDate", "relativeAddress": 0}}, "properties": {"member": "varSelectedDate", "module": "App", "type": "App"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-UnusedVariables", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.SearchItems", "relativeAddress": 0}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 9}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.Items", "relativeAddress": 0}}, "properties": {"member": "Items", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 9}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DisplayName", "relativeAddress": 0}}, "properties": {"member": "DisplayName", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 9}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.MaxLength", "relativeAddress": 0}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 9}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Name_DataCard2.DisplayName", "relativeAddress": 0}}, "properties": {"member": "DisplayName", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Name_DataCard2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 9}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DisplayName", "relativeAddress": 0}}, "properties": {"member": "DisplayName", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 9}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.MaxLength", "relativeAddress": 0}}, "properties": {"member": "<PERSON><PERSON><PERSON><PERSON>", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-InefficientDelayLoading", "ruleIndex": 9}], "tool": {"driver": {"fullName": "PowerApps app checker", "name": "PowerApps app checker", "rules": [{"id": "app-ErrInvalidName", "messageStrings": {"issue": {"text": "Name isn't valid. '{0}' isn't recognized."}}, "properties": {"componentType": "app", "howToFix": ["Remove or correct the reference to the name that isn't valid."], "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-SuggestRemoteExecutionHint", "messageStrings": {"issue": {"text": "Delegation warning. The \"{0}\" part of this formula might not work correctly on large data sets."}}, "properties": {"componentType": "app", "howToFix": ["If your data set exceeds the 500 record limit but contains less than 2,000 records, try resetting the limit.", "Try simplifying the formula.", "Try moving your data to a different data source."], "level": "Medium", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrBadType", "messageStrings": {"issue": {"text": "Invalid argument type."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrInvalidArgs-Func", "messageStrings": {"issue": {"text": "The function '{0}' has some invalid arguments."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "acc-ReadableScreenNameNeeded", "messageStrings": {"issue": {"text": "Revise screen name"}}, "properties": {"componentType": "app", "howToFix": ["Give the screen  a title that describes what's on the screen or what it's used for."], "level": "Low", "primaryCategory": "accessibility", "whyFix": "People who are blind, have low vision, or a reading disability rely on screen titles to navigate using the screen reader. "}}, {"id": "acc-FocusBorderShouldBeVisible", "messageStrings": {"issue": {"text": "Focus isn't showing"}}, "properties": {"componentType": "app", "howToFix": ["Change the FocusedBorderThickness property to be more than 0."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "If the focus isn't visible, people who don't use a mouse won't be able to see it when they're interacting with the app."}}, {"id": "acc-TabIndexShouldBeDefinedForInteractiveControl", "messageStrings": {"issue": {"text": "Missing tab stop"}}, "properties": {"componentType": "app", "howToFix": ["Set TabIndex to 0 or greater to ensure that interactive elements have a tab stop."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "People who use the keyboard with your app will not be able to access this element without a tab stop."}}, {"id": "acc-AccessibleLabelNeeded", "messageStrings": {"issue": {"text": "Missing accessible label"}}, "properties": {"componentType": "app", "howToFix": ["Edit the accessible label property to describe the item."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "If there's no accessible text, people who can’t see the screen won't understand what’s in images and controls."}}, {"id": "app-UnusedVariables", "messageStrings": {"issue": {"text": "Unused variable"}}, "properties": {"componentType": "app", "howToFix": ["Remove the unused variable."], "level": "Medium", "primaryCategory": "performance", "whyFix": "This variable is declared but is not referenced by any control, so it is not needed."}}, {"id": "app-InefficientDelayLoading", "messageStrings": {"issue": {"text": "Inefficient delay loading"}}, "properties": {"componentType": "app", "howToFix": ["Use variables, collections, and navigation context to share state across screens instead."], "level": "Medium", "primaryCategory": "performance", "whyFix": "Controls that reference controls on other screens can slow down app loading and navigation. Doing this may force the app to load the other screens immediately, rather than waiting until the user navigates to that screen."}}], "version": "1.346"}}}], "version": "2.1.0"}
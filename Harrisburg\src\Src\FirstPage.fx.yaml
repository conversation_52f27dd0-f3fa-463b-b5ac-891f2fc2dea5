"FirstPage As screen.'phoneBrowseLayout_ver3.0'":
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)

    RectQuickActionBar1 As rectangle:
        BorderColor: =RGBA(131, 24, 75, 1)
        Fill: =RGBA(211, 66, 9, 1)
        Height: =49.58
        Width: =Parent.Width
        Y: =0.00
        ZIndex: =1

    LblAppName1 As label:
        Color: =RGBA(255, 255, 255, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Fill: =RGBA(58, 58, 58, 0)
        Height: =49.58
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =15.21
        Text: ="Employee Time Off"
        Width: =Parent.Width - Self.X - IconSortUpDown1.Width - IconNewItem1.Width - IconRefresh1.Width
        Wrap: =false
        X: =20
        Y: =0.00
        ZIndex: =2

    IconRefresh1 As icon.Reload:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =60
        Icon: =Icon.Reload
        OnSelect: =Refresh([@TimeOffIndiana])
        PaddingBottom: =12.39
        PaddingLeft: =12.39
        PaddingRight: =22
        PaddingTop: =22
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Refresh list"
        Width: =100
        X: =Parent.Width - IconNewItem1.Width - IconSortUpDown1.Width - Self.Width
        Y: =-10
        ZIndex: =3

    IconSortUpDown1 As icon.ArrowsUpDown:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =60
        Icon: =Icon.Sort
        OnSelect: |-
            =UpdateContext({SortDescending1: !SortDescending1})
        PaddingBottom: =14.65
        PaddingLeft: =14.65
        PaddingRight: =26
        PaddingTop: =26
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: =If(SortDescending1, "Sort list in reverse alphabetical order", "Sort list in alphabetical order")
        Width: =100
        X: =Parent.Width - IconNewItem1.Width - Self.Width
        Y: =-10
        ZIndex: =4

    IconNewItem1 As icon.Add:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        DisplayMode: =If(DataSourceInfo([@TimeOffIndiana], DataSourceInfo.CreatePermission), DisplayMode.Edit, DisplayMode.Disabled)
        Height: =70
        Icon: =Icon.Add
        OnSelect: |-
            =Navigate(
              EnterData,
              ScreenTransition.None,
              { recordToEdit: Blank() }
            )
        PaddingBottom: =13.52
        PaddingLeft: =13.52
        PaddingRight: =24
        PaddingTop: =24
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Create new item"
        Width: =100
        X: =Parent.Width - Self.Width
        Y: =-10
        ZIndex: =5

    Rectangle11 As rectangle:
        BorderColor: =RGBA(131, 24, 75, 1)
        Fill: =RGBA(131, 24, 75, 1)
        Height: =1.13
        Visible: =false
        Width: =Parent.Width
        Y: =87.89
        ZIndex: =8

    "BrowseGallery1 As gallery.'BrowseLayout_Vertical_ThreeTextVariant_ver5.0'":
        BorderColor: =RGBA(131, 24, 75, 1)
        DelayItemLoading: =true
        Height: =500
        Items: |-
            =SortByColumns(
                Filter(
                    [@TimeOffIndiana], // Your data source
                    // Condition 1: Existing search filter
                    //StartsWith(Name, TextSearchBox1.Text)
                    //&& // Combine search filter AND approval filter
                    // Condition 2: New approval filter based on dropdown
                    Switch(
                        ddApprovalFilter.Selected.Value, // Check dropdown selection
                        "Approved", Approval = true,  // If "Approved", filter where the column is true
                        "Denied",   Approval = false, // If "Denied", filter where the column is false (or use Not(YourApprovalColumnName))
                        "All",      true,                         // If "All", this condition is always true (no filtering on approval)
                        true                                  // Default case (also no filtering)
                    )
                    &&
                     'Leave From' >= DatePicker_StartFirstPage.SelectedDate
                     &&
                      'Leave From' <= DatePicker_EndFirstPage.SelectedDate
                    
                ),
                "LeaveFrom", // Column to sort by
                If(
                    SortDescending1,
                    SortOrder.Descending,
                    SortOrder.Ascending
                )
            )
        Layout: =Layout.Vertical
        LoadingSpinner: =LoadingSpinner.Data
        OnSelect: |-
            =UpdateContext({ selectedRecord: ThisItem })
        ShowScrollbar: =false
        TemplatePadding: =0.00
        TemplateSize: =102
        Width: =Parent.Width*0.35
        Y: =122
        ZIndex: =9

        Title1 As label:
            Color: =RGBA(50, 49, 48, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Font: =Font.Georgia
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =34
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =15
            Text: =ThisItem.Name
            VerticalAlign: =VerticalAlign.Top
            Width: =Parent.TemplateWidth - 86
            X: =16
            Y: =2
            ZIndex: =1

        LeaveFrom As label:
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =29
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =10
            Text: =ThisItem.Title
            VerticalAlign: =VerticalAlign.Top
            Width: =Title1.Width
            X: =Title1.X
            Y: =36
            ZIndex: =2

        LeaveTo As label:
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =20
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =10
            Text: =ThisItem.'Leave From'
            VerticalAlign: =VerticalAlign.Top
            Width: =Title1.Width
            X: =Title1.X
            Y: =65
            ZIndex: =3

        NextArrow1 As icon.ChevronRight:
            AccessibleLabel: =Self.Tooltip
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(166, 166, 166, 1)
            DisabledBorderColor: =RGBA(166, 166, 166, 1)
            DisabledColor: =RGBA(244, 244, 244, 1)
            Height: =28.17
            Icon: =Icon.ChevronRight
            OnSelect: =Select(Parent)
            PaddingBottom: =9.01
            PaddingLeft: =9.01
            PaddingRight: =16
            PaddingTop: =16
            Tooltip: ="View item details"
            Width: =50
            X: =Parent.TemplateWidth - Self.Width - 12
            Y: =18.59
            ZIndex: =4

        Separator1 As rectangle:
            BorderColor: =RGBA(131, 24, 75, 1)
            Fill: =RGBA(255, 255, 255, 1)
            Height: =4.51
            OnSelect: =Select(Parent)
            Width: =Parent.TemplateWidth
            Y: =60.85
            ZIndex: =5

        Rectangle1 As rectangle:
            BorderColor: =RGBA(131, 24, 75, 1)
            Fill: =RGBA(131, 24, 75, 1)
            Height: =60.85
            OnSelect: =
            Visible: =ThisItem.IsSelected
            Width: =4
            Y: =0.00
            ZIndex: =6

    Container2 As groupContainer.horizontalAutoLayoutContainer:
        DropShadow: =DropShadow.Light
        Height: =590
        LayoutMode: =LayoutMode.Auto
        RadiusBottomLeft: =4
        RadiusBottomRight: =4
        RadiusTopLeft: =4
        RadiusTopRight: =4
        Width: =636
        X: =498
        Y: =50
        ZIndex: =10

        DetailForm1 As formViewer:
            BorderColor: =RGBA(131, 24, 75, 1)
            DataSource: =TimeOffIndiana
            Height: =590.42
            Item: =selectedRecord
            LayoutMinHeight: =250
            LayoutMinWidth: =320
            Width: =Parent.Width*0.5
            Y: =49
            ZIndex: =1

            Name_DataCard2 As typedDataCard.textualViewCard:
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="Name"
                Default: =ThisItem.Name
                DisplayMode: =DisplayMode.View
                DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Name)
                Fill: =RGBA(0, 0, 0, 0)
                Height: =40
                Required: =false
                Width: =636
                X: =0
                Y: =0
                ZIndex: =2

                DataCardKey11 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =11.83
                    Text: =Parent.DisplayName
                    Width: =Parent.Width - 60
                    Wrap: =false
                    X: =30
                    Y: =10
                    ZIndex: =1

                DataCardValue9 As label:
                    AutoHeight: =true
                    BorderColor: =RGBA(131, 24, 75, 1)
                    Color: =RGBA(0, 0, 0, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    DisplayMode: =Parent.DisplayMode
                    PaddingLeft: =0
                    PaddingRight: =0
                    PaddingTop: =0
                    Size: =11.83
                    Text: =Parent.Default
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardKey11.Y + DataCardKey11.Height + 5
                    ZIndex: =2

            "'Leave From_DataCard1' As typedDataCard.textualViewCard":
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="LeaveFrom"
                Default: =ThisItem.'Leave From'
                DisplayMode: =DisplayMode.View
                DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave From')
                Fill: =RGBA(0, 0, 0, 0)
                Height: =40
                Required: =false
                Width: =636
                X: =0
                Y: =1
                ZIndex: =2

                DataCardKey2 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =11.83
                    Text: =Parent.DisplayName
                    Width: =Parent.Width - 60
                    Wrap: =false
                    X: =30
                    Y: =10
                    ZIndex: =1

                DataCardValue2 As label:
                    AutoHeight: =true
                    BorderColor: =RGBA(131, 24, 75, 1)
                    Color: =RGBA(0, 0, 0, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    DisplayMode: =Parent.DisplayMode
                    PaddingLeft: =0
                    PaddingRight: =0
                    PaddingTop: =0
                    Size: =11.83
                    Text: =Parent.Default
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardKey2.Y + DataCardKey2.Height + 5
                    ZIndex: =2

            "'Leave To_DataCard1' As typedDataCard.textualViewCard":
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="LeaveTo"
                Default: =ThisItem.'Leave To'
                DisplayMode: =DisplayMode.View
                DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave To')
                Fill: =RGBA(0, 0, 0, 0)
                Height: =40
                Required: =false
                Width: =636
                X: =0
                Y: =1
                ZIndex: =2

                DataCardKey3 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =11.83
                    Text: =Parent.DisplayName
                    Width: =Parent.Width - 60
                    Wrap: =false
                    X: =30
                    Y: =10
                    ZIndex: =1

                DataCardValue3 As label:
                    AutoHeight: =true
                    BorderColor: =RGBA(131, 24, 75, 1)
                    Color: =RGBA(0, 0, 0, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    DisplayMode: =Parent.DisplayMode
                    PaddingLeft: =0
                    PaddingRight: =0
                    PaddingTop: =0
                    Size: =11.83
                    Text: =Parent.Default
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardKey3.Y + DataCardKey3.Height + 5
                    ZIndex: =2

            Location_DataCard1 As typedDataCard.textualViewCard:
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="Location"
                Default: =ThisItem.Location
                DisplayMode: =DisplayMode.View
                DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Location)
                Fill: =RGBA(0, 0, 0, 0)
                Height: =40
                Required: =false
                Width: =636
                X: =0
                Y: =2
                ZIndex: =2

                DataCardKey4 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =11.83
                    Text: =Parent.DisplayName
                    Width: =Parent.Width - 60
                    Wrap: =false
                    X: =30
                    Y: =10
                    ZIndex: =1

                DataCardValue4 As label:
                    AutoHeight: =true
                    BorderColor: =RGBA(131, 24, 75, 1)
                    Color: =RGBA(0, 0, 0, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    DisplayMode: =Parent.DisplayMode
                    PaddingLeft: =0
                    PaddingRight: =0
                    PaddingTop: =0
                    Size: =11.83
                    Text: =Parent.Default
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardKey4.Y + DataCardKey4.Height + 5
                    ZIndex: =2

            Approval_DataCard1 As typedDataCard.toggleViewCard:
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="Approval"
                Default: =ThisItem.Approval
                DisplayMode: =DisplayMode.View
                DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Approval)
                Fill: =RGBA(0, 0, 0, 0)
                Height: =50
                Required: =false
                Width: =636
                X: =0
                Y: =3
                ZIndex: =2

                DataCardKey5 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =11.83
                    Text: =Parent.DisplayName
                    Width: =Parent.Width - 60
                    Wrap: =false
                    X: =30
                    Y: =10
                    ZIndex: =1

                DataCardValue5 As toggleSwitch:
                    BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                    Color: =RGBA(0, 0, 0, 1)
                    Default: =Parent.Default
                    DisplayMode: =Parent.DisplayMode
                    FalseFill: =RGBA(128, 130, 133, 1)
                    FalseText: ="No"
                    HandleFill: =RGBA(255, 255, 255, 1)
                    Height: =49
                    Size: =11.83
                    TrueFill: =RGBA(211, 66, 9, 1)
                    TrueText: ="Yes"
                    Width: =154
                    X: =30
                    Y: =DataCardKey5.Y + DataCardKey5.Height + 5
                    ZIndex: =2

            Role_DataCard2 As typedDataCard.textualViewCard:
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="Role"
                Default: =ThisItem.Role
                DisplayMode: =DisplayMode.View
                DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,Role)
                Fill: =RGBA(0, 0, 0, 0)
                Height: =40
                Required: =false
                Width: =636
                X: =0
                Y: =5
                ZIndex: =2

                DataCardKey15 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =11.83
                    Text: =Parent.DisplayName
                    Width: =Parent.Width - 60
                    Wrap: =false
                    X: =30
                    Y: =10
                    ZIndex: =1

                DataCardValue13 As label:
                    AutoHeight: =true
                    BorderColor: =RGBA(131, 24, 75, 1)
                    Color: =RGBA(0, 0, 0, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    DisplayMode: =Parent.DisplayMode
                    PaddingLeft: =0
                    PaddingRight: =0
                    PaddingTop: =0
                    Size: =11.83
                    Text: =Parent.Default
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardKey15.Y + DataCardKey15.Height + 5
                    ZIndex: =2

            "'Leave Type_DataCard2' As typedDataCard.comboBoxViewCard":
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="LeaveType"
                Default: =ThisItem.'Leave Type'
                DisplayMode: =DisplayMode.View
                DisplayName: =DataSourceInfo([@TimeOffIndiana],DataSourceInfo.DisplayName,'Leave Type')
                Fill: =RGBA(0, 0, 0, 0)
                Height: =50
                Required: =false
                Update: =DataCardValue14.Selected
                Width: =636
                X: =0
                Y: =6
                ZIndex: =2

                DataCardKey16 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =11.83
                    Text: =Parent.DisplayName
                    Width: =Parent.Width - 60
                    Wrap: =false
                    X: =30
                    Y: =10
                    ZIndex: =1

                DataCardValue14 As combobox:
                    BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                    ChevronBackground: =RGBA(211, 66, 9, 1)
                    ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
                    ChevronDisabledFill: =RGBA(244, 244, 244, 1)
                    ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
                    ChevronHoverFill: =RGBA(255, 255, 255, 1)
                    DefaultSelectedItems: =Parent.Default
                    DisabledBorderColor: =RGBA(166, 166, 166, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    DisabledFill: =RGBA(244, 244, 244, 1)
                    DisplayMode: =Parent.DisplayMode
                    Height: =39.44
                    HoverColor: =RGBA(0, 0, 0, 1)
                    HoverFill: =RGBA(255, 211, 205, 1)
                    Items: =Choices([@TimeOffIndiana].'Leave Type')
                    PaddingLeft: =If(Self.DisplayMode = DisplayMode.Edit, 5, 0)
                    PressedColor: =RGBA(255, 255, 255, 1)
                    PressedFill: =RGBA(131, 24, 75, 1)
                    SelectionFill: =RGBA(211, 66, 9, 1)
                    SelectMultiple: =false
                    Size: =11.83
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardKey16.Y + DataCardKey16.Height + 5
                    ZIndex: =2

                ErrorMessage6 As label:
                    AutoHeight: =true
                    Color: =RGBA(168, 0, 0, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =10
                    Live: =Live.Assertive
                    PaddingBottom: =0
                    PaddingLeft: =0
                    PaddingRight: =0
                    PaddingTop: =0
                    Size: =24
                    Text: =Parent.Error
                    Visible: =Parent.DisplayMode=DisplayMode.Edit
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardValue14.Y + DataCardValue14.Height
                    ZIndex: =3

                StarVisible6 As label:
                    Align: =Align.Center
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =DataCardKey16.Height
                    Size: =21
                    Text: ="*"
                    Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                    Width: =30
                    Wrap: =false
                    Y: =DataCardKey16.Y
                    ZIndex: =4

    IconEdit As icon.Edit:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(131, 24, 75, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =35
        Icon: =Icon.Edit
        OnSelect: |-
            =Set( gblRecordToEdit, selectedRecord );
            Navigate( EditRecordScreen, ScreenTransition.None );
        Width: =46
        X: =1054
        Y: =72
        ZIndex: =11

    IconDelete As icon.Trash:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(131, 24, 75, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =35
        Icon: =Icon.Trash
        OnSelect: |
            =// 1) Always re-capture the record under the trash can…
            Set( gblRecordToDelete, selectedRecord );
            
            // 2) If we've already “armed” this record, actually delete it
            If(
              varDeleteArmed && gblRecordToDelete.ID = selectedRecord.ID,
              
              // ——> SECOND TAP: do the real delete
              Remove( TimeOffIndiana, gblRecordToDelete );
              Notify( "Record deleted", NotificationType.Success );
              Set( varDeleteArmed, false );
              Refresh( TimeOffIndiana );     // so the gallery updates
              // optional: Back()    // if you want to leave the screen
              
              ,
              
              // ——> FIRST TAP: “arm” the delete and warn the user
              Set( varDeleteArmed, true );
              Notify( "Tap delete again to confirm", NotificationType.Warning )
            )
        Width: =52
        X: =973
        Y: =64
        ZIndex: =12

    ddApprovalFilter As dropdown:
        BorderColor: =RGBA(131, 24, 75, 1)
        ChevronBackground: =RGBA(211, 66, 9, 1)
        ChevronDisabledBackground: =RGBA(166, 166, 166, 1)
        ChevronDisabledFill: =RGBA(244, 244, 244, 1)
        ChevronHoverBackground: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        ChevronHoverFill: =RGBA(255, 255, 255, 1)
        Default: ="All"
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Height: =27
        HoverColor: =RGBA(0, 0, 0, 1)
        HoverFill: =RGBA(255, 211, 205, 1)
        Items: =["All", "Approved", "Denied"]
        PressedColor: =RGBA(255, 255, 255, 1)
        PressedFill: =RGBA(131, 24, 75, 1)
        SelectionFill: =RGBA(211, 66, 9, 1)
        Size: =11.83
        Width: =118
        X: =364
        Y: =75
        ZIndex: =13

    DatePicker_EndFirstPage As datepicker:
        BorderColor: =RGBA(131, 24, 75, 1)
        CalendarHeaderFill: =RGBA(211, 66, 9, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Height: =27
        HoverDateFill: =RGBA(255, 211, 205, 1)
        IconBackground: =RGBA(211, 66, 9, 1)
        SelectedDateFill: =RGBA(211, 66, 9, 1)
        Size: =13.52
        Width: =154
        X: =193
        Y: =75
        ZIndex: =18

    DatePicker_StartFirstPage As datepicker:
        BorderColor: =RGBA(131, 24, 75, 1)
        CalendarHeaderFill: =RGBA(211, 66, 9, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(242, 242, 242, 0.01)
        Height: =29
        HoverDateFill: =RGBA(255, 211, 205, 1)
        IconBackground: =RGBA(211, 66, 9, 1)
        PaddingBottom: =2.82
        PaddingLeft: =6.76
        SelectedDateFill: =RGBA(211, 66, 9, 1)
        Size: =13.52
        Width: =154
        X: =20
        Y: =73
        ZIndex: =19

    Label7 As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =32
        Size: =11.83
        Text: ="From"
        Width: =119
        X: =20
        Y: =48
        ZIndex: =20

    Label7_1 As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =32
        Size: =11.83
        Text: ="To"
        Width: =119
        X: =193
        Y: =48
        ZIndex: =21

    Label7_2 As label:
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(0, 0, 0, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =32
        Size: =11.83
        Text: ="Approval"
        Width: =119
        X: =363
        Y: =49
        ZIndex: =22

